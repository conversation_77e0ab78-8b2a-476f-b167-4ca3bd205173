{"name": "tmj-zlb-vue2", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "format": "prettier --write \"src/**/*.{js,vue,json,css,scss}\"", "stylelint": "stylelint --cache --fix \"**/*.{less,css,scss,vue}\" --cache --cache-location node_modules/.cache/stylelint/", "prepare": "husky install", "lint-staged": "lint-staged"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,vue}": ["vue-cli-service lint", "prettier --write"], "*.{css,scss,less,vue}": ["stylelint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"], "package.json": ["prettier --write", "npx sort-package-json"]}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "dependencies": {"@aligov/jssdk-mgop": "^3.1.9", "axios": "^1.10.0", "core-js": "^3.8.3", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "echarts": "^5.6.0", "lodash": "^4.17.21", "normalize.css": "^8.0.1", "vant": "^2.13.8", "vue": "^2.6.14", "vue-echarts": "^7.0.3", "vue-router": "^3.5.4", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@tailwindcss/language-server": "0.0.12", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "autoprefixer": "^10.4.21", "babel-plugin-import": "^1.13.8", "eslint": "^7.32.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-tailwindcss": "^3.18.0", "eslint-plugin-vue": "^8.0.3", "husky": "^4.3.8", "lint-staged": "^10.5.4", "postcss": "^8.5.6", "postcss-html": "^0.36.0", "postcss-pxtorem": "^6.1.0", "postcss-scss": "^4.0.9", "prettier": "^2.8.8", "sass": "^1.32.13", "sass-loader": "^10.1.1", "sort-package-json": "^1.49.0", "stylelint": "^13.13.1", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard": "^20.0.0", "stylelint-order": "^5.0.0", "stylelint-scss": "^4.7.0", "tailwindcss": "^3.4.17", "vue-template-compiler": "^2.6.14"}, "engines": {"node": "14.x", "npm": ">=6.0.0"}}