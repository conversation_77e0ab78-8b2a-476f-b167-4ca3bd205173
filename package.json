{"name": "react-ts-questionnaire", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^5.6.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "ahooks": "^3.8.5", "antd": "^5.25.4", "axios": "^1.9.0", "classnames": "^2.5.1", "dayjs": "^1.11.13", "immer": "^10.1.1", "lodash": "^4.17.21", "normalize.css": "^8.0.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-infinite-scroll-component": "^6.1.0", "react-router": "^7.6.1", "react-scripts": "5.0.1", "typescript": "^4.9.5", "use-immer": "^0.11.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "prepare": "husky", "commitlint": "commitlint --edit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@craco/craco": "^7.1.0", "@types/lodash": "^4.17.18", "autoprefixer": "^10.4.21", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "husky": "^9.1.7", "postcss": "^8.5.4", "prettier": "^3.5.3", "sass": "^1.89.1", "tailwindcss": "^3.4.17"}}