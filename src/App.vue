<script>
export default {
  name: 'App',
  computed: {
    // ...mapGetters('elderMode', ['isElderMode', 'elderModeClasses']),
  },
  methods: {
    // ...mapActions('elderMode', ['initElderMode']),
  },
  created() {
    // 应用启动时初始化适老版状态
    // this.initElderMode()
  },
}
</script>

<template>
  <router-view></router-view>
</template>

<style lang="scss">
//#app {
//  /* font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; */
//  -webkit-font-smoothing: antialiased;
//  -moz-osx-font-smoothing: grayscale;
//  color: #333333;
//}
</style>
