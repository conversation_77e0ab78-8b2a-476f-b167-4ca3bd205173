<script>
import dayjs from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear'
import { isEmpty } from '@/utils'
import DateSwitcher from '@/components/DateSwitcher.vue'

export default {
  name: 'CalendarComponent',

  components: {
    // 注册 DateSwitcher，如果你是全局注册可省略
    DateSwitcher,
  },

  props: {
    initialDate: {
      type: [String, Date],
      default: () => new Date(),
    },
    format: {
      type: String,
      default: 'YYYY-MM-DD',
    },
    list: {
      type: Object,
      default: () => ({}),
    },
    customClass: {
      type: String,
      default: '',
    },
    customStyle: {
      type: [String, Object],
      default: '',
    },
    mode: {
      type: String,
      default: 'day',
    },
    unit: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      currentDate: this.initialDate,
      calendarDays: [],
      monthsData: [],
      animation: {
        direction: 'next',
        active: false,
        isAnimating: false,
      },
      weekdays: ['日', '一', '二', '三', '四', '五', '六'],
    }
  },

  computed: {
    switcherType() {
      const map = {
        day: 'month',
        month: 'year',
        quarter: 'year',
        year: 'year',
      }
      return map[this.mode] || 'month'
    },
    isDay() {
      return this.mode === 'day'
    },
    isYear() {
      return this.mode === 'year'
    },
    isGrid() {
      return ['month', 'quarter', 'year'].includes(this.mode)
    },
  },

  watch: {
    mode: 'updateData',
    list: {
      handler: 'updateData',
      deep: true,
    },
    currentDate(newDate, oldDate) {
      this.updateData()
      if (oldDate) this.startTransitionAnimation(newDate, oldDate)
    },
  },

  mounted() {
    dayjs.extend(quarterOfYear)
    this.updateData()
  },

  methods: {
    isEmpty,

    isToday(date) {
      return date && dayjs(date).isSame(dayjs(), 'day')
    },
    isCurrentMonth(month) {
      return month && dayjs(month).isSame(dayjs(), 'month')
    },
    isCurrentQuarter(quarter) {
      quarter = quarter.split('Q')[1]
      return dayjs().quarter() === Number(quarter)
    },
    isCurrentYear(year) {
      return year && dayjs(year).isSame(dayjs(), 'year')
    },
    isFutureDate(date) {
      return date && dayjs(date).isAfter(dayjs(), 'day')
    },
    isFutureTimeUnit(dateStr) {
      if (!dateStr) return false
      const now = dayjs()
      if (this.mode === 'month') return dayjs(dateStr).isAfter(now, 'month')
      if (this.mode === 'quarter') {
        const [year, q] = dateStr.split('-')
        const quarter = Number(q.replace('Q', ''))
        return Number(year) > now.year() || (Number(year) === now.year() && quarter > now.quarter())
      }
      if (this.mode === 'year') return Number(dateStr) > now.year()
      return false
    },

    getCellStyleClass(amount, isHighlight) {
      const num = Number(amount)
      if (isHighlight) {
        if (num < 0) return 'bg-green-today text-white'
        if (num > 0) return 'bg-red-today text-white'
        return '!bg-blue-500 text-white'
      }
      if (num < 0) return 'bg-green__down text-green__down'
      if (num > 0) return 'bg-red__up text-red__up'
      return 'text-gray__neutral bg-default__neutral'
    },

    formatAmount(amount, isPrefix = true) {
      const num = Number(amount)
      if (isNaN(num) || num === 0) return '0.00'
      let formatted = ''
      const abs = Math.abs(num)
      if (this.unit === 'w') formatted = (abs / 10000).toFixed(2) + 'w'
      else if (this.unit === 'k') formatted = (abs / 1000).toFixed(2) + 'k'
      else formatted = abs.toFixed(2)
      return isPrefix ? (num > 0 ? '+' + formatted : '-' + formatted) : formatted
    },
    getAnimationClass(direction) {
      return {
        'slide-right-to-left': this.animation.active && direction === 'next',
        'slide-left-to-right': this.animation.active && direction === 'prev',
      }
    },
    handleDateClick(date, amount) {
      if (date) this.$emit('dateClick', date, amount)
    },
    handleGridItemClick(item, amount) {
      if (item) this.$emit('dateClick', item, amount)
    },
    handleDateSwitcherChange() {
      const oldDate = this.currentDate
      this.updateData()
      this.startTransitionAnimation(this.currentDate, oldDate)
      this.$emit('dateSwitch', this.currentDate)
    },
    getIsHighlight(date) {
      if (this.mode === 'day') return this.isToday(date)
      if (this.mode === 'month') return this.isCurrentMonth(date)
      if (this.mode === 'quarter') return this.isCurrentQuarter(date)
      if (this.mode === 'year') return this.isCurrentYear(date)
      return false
    },
    updateData() {
      this.isDay ? this.generateCalendarDays() : this.generateGridData()
    },
    generateCalendarDays() {
      const date = dayjs(this.currentDate)
      const year = date.year()
      const month = date.month()
      const firstDay = dayjs(new Date(year, month, 1)).day()
      const daysInMonth = date.daysInMonth()

      const emptyDays = Array.from({ length: firstDay }, () => ({ date: '', day: 0, amount: 0 }))
      const filledDays = Array.from({ length: daysInMonth }, (_, i) => {
        const d = i + 1
        const dateStr = dayjs(new Date(year, month, d)).format(this.format)
        const amount = Number(this.list[dateStr] || 0)
        return { date: dateStr, day: d, amount }
      })
      this.calendarDays = [...emptyDays, ...filledDays]
    },
    generateGridData() {
      const map = {
        month: this.generateMonthsData,
        quarter: this.generateQuartersData,
        year: this.generateYearsData,
      }
      const fn = map[this.mode]
      if (fn) fn()
    },
    generateMonthsData() {
      const year = dayjs(this.currentDate).year()
      this.monthsData = Array.from({ length: 12 }, (_, i) => {
        const m = i + 1
        const monthStr = `${year}-${String(m).padStart(2, '0')}`
        return {
          month: monthStr,
          label: `${m}月`,
          amount: Number(this.list[monthStr] || 0),
        }
      })
    },
    generateQuartersData() {
      const year = dayjs(this.currentDate).year()
      const labels = ['第一季度', '第二季度', '第三季度', '第四季度']
      this.monthsData = Array.from({ length: 4 }, (_, i) => {
        const q = i + 1
        const quarterStr = `${year}-Q${q}`
        return {
          month: quarterStr,
          label: labels[i],
          amount: Number(this.list[labels[i]] || 0),
        }
      })
    },
    generateYearsData() {
      console.log('🚀 ~ generateYearsData ~ this.data:', this.list)
      const years = Object.keys(this.list)
        .filter((k) => /^\d{4}$/.test(k))
        .sort((a, b) => Number(a) - Number(b))
      this.monthsData = years.map((y) => ({
        month: y,
        label: `${y}年`,
        amount: Number(this.list[y] || 0),
      }))

      console.log('this.monthsData', this.monthsData)
    },
    startTransitionAnimation(newDate, oldDate) {
      if (!oldDate) return
      this.animation.direction = dayjs(newDate).isAfter(dayjs(oldDate)) ? 'next' : 'prev'
      const start = () => {
        this.animation.active = true
        this.animation.isAnimating = true
        setTimeout(() => {
          this.animation.active = false
          this.animation.isAnimating = false
        }, 500)
      }
      if (this.animation.isAnimating) {
        this.animation.active = false
        setTimeout(start, 16)
      } else {
        start()
      }
    },
  },
}
</script>

<template>
  <div class="calendar-container" :class="customClass" :style="customStyle">
    <!-- 日期切换器 -->
    <DateSwitcher
      v-if="!isYear"
      v-model="currentDate"
      :type="switcherType"
      @change="handleDateSwitcherChange"
    />

    <!-- 日视图 -->
    <template v-if="isDay">
      <!-- 星期头部 -->
      <div class="week-header">
        <div v-for="(day, index) in weekdays" :key="index" class="week-day">
          {{ day }}
        </div>
      </div>

      <!-- 日历主体 -->
      <div class="calendar-wrapper">
        <div class="calendar-body" :class="getAnimationClass(animation.direction)">
          <div
            v-for="(item, index) in calendarDays"
            :key="index"
            class="calendar-cell"
            :class="[item.day ? getCellStyleClass(item.amount, isToday(item.date)) : '']"
            @click="handleDateClick(item.date, item.amount)"
          >
            <div
              v-if="item.day"
              class="cell-day"
              :class="[item.amount || isToday(item.date) ? 'text-inherit' : 'text-dark']"
            >
              {{ isToday(item.date) ? '今天' : item.day }}
            </div>
            <div
              v-if="!isEmpty(item.amount) && !isEmpty(item.date) && !isFutureDate(item.date)"
              class="cell-amount"
            >
              {{ formatAmount(item.amount, false) }}
            </div>
          </div>
        </div>
      </div>

      <div class="flex gap-x-4">
        <span class="text-red__up"> 正：+ </span>
        <span class="text-green__down"> 负：- </span>
      </div>
    </template>

    <!-- 网格视图（月/季/年） -->
    <template v-else-if="isGrid">
      <div class="grid-wrapper">
        <div class="month-grid" :class="getAnimationClass(animation.direction)">
          <div
            v-for="(item, index) in monthsData"
            :key="index"
            class="month-cell"
            :class="[getCellStyleClass(item.amount, getIsHighlight(item.month))]"
            @click="handleGridItemClick(item.month, item.amount)"
          >
            <div
              class="month-label"
              :class="[item.amount || getIsHighlight(item.month) ? 'text-inherit' : 'text-dark']"
            >
              {{ item.label }}
            </div>
            <div v-if="!isFutureTimeUnit(item.month)" class="month-amount">
              {{ formatAmount(item.amount) }}
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
.calendar-container {
  border-radius: 6px;
  padding: 10px;
  width: 100%;
  background-color: #ffffff;
}

.week-header {
  display: flex;
  margin-top: 10px;
  margin-bottom: 5px;
  width: 100%;
}

.week-day {
  padding: 10px 0;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  color: #333333;
  flex: 1;
}

// 共享的容器样式
%view-wrapper {
  position: relative;

  /* overflow: hidden; */
  padding: 5px 0;
}

// 共享的动画网格样式
%animated-grid {
  transition: transform 0ms;
  will-change: transform, opacity;

  &.slide-right-to-left,
  &.slide-left-to-right {
    transition: transform 0ms, opacity 0ms;
  }
}

.calendar-wrapper {
  @extend %view-wrapper;
}

.grid-wrapper {
  @extend %view-wrapper;
}

.calendar-body {
  @extend %animated-grid;

  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-gap: 4px;
  width: 100%;
}

.month-grid {
  @extend %animated-grid;

  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 8px;
  margin-top: 10px;
}

// 共享的单元格基础样式
%base-cell {
  position: relative;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  flex-direction: column;
}

.calendar-cell {
  @extend %base-cell;

  aspect-ratio: 1;

  &-hover {
    background-color: #f9f9f9;
  }
}

.month-cell {
  @extend %base-cell;

  padding: 20rpx 0;
  background-color: #f7f7f7;
  aspect-ratio: 1;

  &-hover {
    opacity: 0.9;
  }
}

.cell-day {
  font-size: 14px;
  font-weight: 350;
}

.cell-amount {
  font-size: 12px;
  font-weight: 350;
  text-align: center;
  word-break: break-all;
}

.month-label {
  font-size: 16px;
  font-weight: 400;
}

.month-amount {
  font-size: 16px;
  font-weight: 500;
}

/* 颜色样式 */
.text-white {
  color: #ffffff;
}

.text-dark {
  color: #333333;
}

.text-red__up {
  color: #f56c6c;
}

.text-green__down {
  color: #00b384;
}

.text-gray__neutral {
  color: #999999;
}

.bg-red__up {
  background-color: rgba($color: #f56c6c, $alpha: 0.1);
}

.bg-green__down {
  background-color: rgba($color: #00b384, $alpha: 0.1);
}

.bg-default__neutral {
  background-color: #f7f7f7;
}

.bg-red-today {
  background-color: #f15556;
}

.bg-green-today {
  background-color: #00b384;
}

/* 动画 */
.slide-right-to-left {
  animation: slideRightToLeft 0.5s forwards;
}

.slide-left-to-right {
  animation: slideLeftToRight 0.5s forwards;
}

@keyframes slideRightToLeft {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideLeftToRight {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}
</style>
