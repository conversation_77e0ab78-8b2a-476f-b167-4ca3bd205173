<script>
import VChart from 'vue-echarts'
import * as Charts from 'echarts/charts'
import * as ChartComponents from 'echarts/components'
import * as ChartFeatures from 'echarts/features'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { addUnit, isColor } from '@/utils'
import { CHARTS_MAP, COMPONENTS_MAP } from './constant'

export default {
  name: 'VueEChart',
  components: { VChart },
  props: {
    initOption: {
      type: Object,
      default: () => ({}),
    },
    option: {
      type: Object,
      required: true,
    },
    group: {
      type: String,
      default: '',
    },
    theme: {
      type: String,
      default: 'default',
    },
    autoresize: {
      type: Boolean,
      default: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    loadingOptions: {
      type: Object,
      default: () => ({
        text: '加载中...',
        color: '#37f',
        textColor: 'black',
        maskColor: 'rgba(255, 255, 255, 1)',
        zlevel: 0,
      }),
    },
    manualUpdate: {
      type: Boolean,
      default: false,
    },
    width: {
      type: [String, Number],
      default: '100%',
    },
    height: {
      type: [String, Number],
      default: '400px',
    },
  },
  data() {
    return {}
  },
  computed: {
    chartStyle() {
      return {
        ...(this.$attrs.style ? this.$attrs.style : {}),
        backgroundColor: this.theme && isColor(this.theme) ? this.theme : undefined,
        width: addUnit(this.width),
        height: addUnit(this.height),
      }
    },
  },
  beforeMount() {
    if (!this.option) return
    const deps = []
    Object.keys(this.option).forEach((key) => {
      if (COMPONENTS_MAP[key]) {
        deps.push(COMPONENTS_MAP[key])
      }
    })

    const charts = []
    const features = []
    const series = Array.isArray(this.option.series) ? this.option.series : [this.option.series]
    series.forEach((item) => {
      if (CHARTS_MAP[item.type]) {
        charts.push(CHARTS_MAP[item.type])
      }

      if (item.labelLayout) {
        features.push('LabelLayout')
      }
      if (item.universalTransition) {
        features.push('UniversalTransition')
      }
    })

    use([
      CanvasRenderer,
      ...Array.from(new Set(charts)).map((chart) => Charts[chart]),
      ...deps.map((dep) => ChartComponents[dep]),
      ...Array.from(new Set(features)).map((feature) => ChartFeatures[feature]),
    ])
  },
}
</script>

<template>
  <v-chart :style="chartStyle" v-bind="$props" />
</template>
