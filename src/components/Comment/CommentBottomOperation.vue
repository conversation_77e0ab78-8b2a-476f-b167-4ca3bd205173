<script lang="ts">
import Iconfont from '@/components/Iconfont.vue'

export default {
  name: 'CommentBottomOperation',
  components: { Iconfont },
  props: {
    data: {
      type: Object,
      required: true,
    },
    showLike: {
      type: Boolean,
      default: true,
    },
    likeIcon: String,
    activeLikeIcon: String,
    likeIconColor: String,
    activeLikeIconColor: String,
    // 是否显示删除按钮
    showDelete: {
      type: Boolean,
      default: true,
    },
    // 是否显示回复按钮
    showReply: {
      type: Boolean,
      default: true,
    },
  },
}
</script>

<template>
  <div class="mt-1 flex items-center gap-x-2 pr-0">
    <span class="text-xs text-[#666]">{{ data.date }}</span>
    <span v-if="showDelete" class="cursor-pointer text-red-500" @click.stop="$emit('delete')">
      删除
    </span>
    <div class="ml-auto flex items-center gap-x-6 text-xs">
      <div class="cursor-pointer" @click.stop="$emit('like')">
        <Iconfont
          :color="data.likeActive ? activeLikeIconColor : likeIconColor"
          :name="data.likeActive ? activeLikeIcon : likeIcon"
        />
        {{ data.likeCount }}
      </div>
      <div v-if="showReply" class="cursor-pointer" @click.stop="$emit('reply')">
        <Iconfont class="relative -bottom-px" color="#A8A5A4" name="pinglun" size="16" />
        {{ data.likeCount }}
      </div>
    </div>
  </div>
</template>
