<script>
import CommentBottomOperation from './CommentBottomOperation.vue'

/*
@description 评论列表数据格式
interface CommentItem {
  id: string;
  avatar: string;
  nickname: string;
  date: string;
  content: string;
  likeActive: boolean;
  likeCount: number;
  disabledReply: boolean;
  allowDelete: boolean;
  commentCount: number;
  comment: CommentItem[];
}
*/

export default {
  name: 'CommentList',
  components: { CommentBottomOperation },
  props: {
    data: {
      type: Array,
      required: true,
    },
    /** 显示点赞图标 */
    showLike: {
      type: Boolean,
      default: true,
    },
    /** 点赞图标 */
    likeIcon: {
      type: String,
      default: 'dianzan3',
    },
    /** 激活后的点赞图标 */
    activeLikeIcon: {
      type: String,
      default: 'dianzan',
    },
    /** 点赞图标默认颜色 */
    likeIconColor: {
      type: String,
      default: '#A8A5A4',
    },
    /** 点赞图标激活后的颜色 */
    activeLikeIconColor: {
      type: String,
      default: '#F66233',
    },
  },
  data() {
    return {
      listData: [],
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler() {
        this.handleListData()
      },
    },
  },
  methods: {
    // 处理数据
    handleListData() {
      this.listData = this.data.map((item) => ({
        ...item,
        hidden: false,
        commentCount: item.commentCount || 0,
        comment: [],
      }))
    },

    replyClickHandle(item) {
      if (item.disabledReply) return
      this.$emit('reply', { id: item.id, nickname: item.nickname })
    },

    likeClickHandle(id, index, subIndex) {
      let item = subIndex == null ? this.listData[index] : this.listData[index].comment[subIndex]
      item.likeActive = !item.likeActive
      item.likeCount += item.likeActive ? 1 : -1
      this.$emit('like', id)
    },

    replyOperationClickHandle(id) {
      const item = this.listData.find((_) => _.id === id)
      if (item.comment.length >= item.commentCount) {
        item.hidden = !item.hidden
        return
      }
      this.$emit('show-more', {
        id,
        currentCommentCount: item.comment.length,
      })
    },

    // 新增评论数据
    addCommentData(id, data) {
      function _handleCommentData(authorNickname, comment) {
        const commentData = []
        comment.forEach((item) => {
          const {
            id,
            avatar,
            nickname,
            date,
            content,
            likeActive,
            likeCount,
            disabledReply,
            allowDelete,
            comment,
          } = item
          const commentItem = {
            id,
            avatar,
            nickname,
            authorNickname,
            date,
            content,
            likeActive,
            likeCount,
            disabledReply,
            allowDelete,
          }
          commentData.push(commentItem)
          if (comment?.length) {
            commentData.push(..._handleCommentData(nickname, comment))
          }
        })
        return commentData
      }
      // 根据id查询主评论索引
      const commentIndex = this.listData.findIndex((_) => _.id === id)
      if (commentIndex === -1) throw new Error('未找到对应的评论')
      // 插入评论
      this.listData[commentIndex].comment.push(..._handleCommentData('', data))
    },

    // 添加回复评论
    addCommentReply(id, data) {
      let commentIndex = this.listData.findIndex((_) => _.id === id)
      let subCommentIndex = -1
      if (commentIndex === -1) {
        const { index, subIndex } = this._findCommentIdInComment(id)
        commentIndex = index
        subCommentIndex = subIndex
      }
      // 如果在主评论和子评论中都没有找到，则认为是新增评论
      if (commentIndex === -1 && subCommentIndex === -1) {
        this.listData.unshift({
          id: data.id,
          avatar: data.avatar,
          nickname: data.nickname,
          date: data.date,
          content: data.content,
          allowDelete: data.allowDelete,
          likeActive: false,
          likeCount: 0,
          disabledReply: data.disabledReply,
          hidden: false,
          commentCount: 0,
          comment: [],
        })
        return
      }
      // 插入评论
      // 判断插入的评论是主评论还是子评论
      if (subCommentIndex === -1) {
        this.listData[commentIndex].comment.unshift({
          id: data.id,
          avatar: data.avatar,
          nickname: data.nickname,
          authorNickname: '',
          date: data.date,
          content: data.content,
          allowDelete: data.allowDelete,
          likeActive: false,
          likeCount: 0,
          disabledReply: data.disabledReply,
        })
      } else {
        const nickname = this.listData[commentIndex].comment[subCommentIndex].nickname
        this.listData[commentIndex].comment.splice(subCommentIndex + 1, 0, {
          id: data.id,
          avatar: data.avatar,
          nickname: data.nickname,
          authorNickname: nickname,
          date: data.date,
          content: data.content,
          allowDelete: data.allowDelete,
          likeActive: false,
          likeCount: 0,
          disabledReply: false,
        })
      }
      // 增加评论数量
      this.listData[commentIndex].commentCount++
    },

    // 删除回复评论
    deleteCommentReply(id) {
      // 获取评论索引
      let commentIndex = this.listData.findIndex((_) => _.id === id)
      let subCommentIndex = -1
      if (commentIndex === -1) {
        const { index, subIndex } = this._findCommentIdInComment(id)
        commentIndex = index
        subCommentIndex = subIndex
      }

      if (commentIndex === -1 && subCommentIndex === -1) throw new Error('未找到对应的评论')

      // 删除评论
      // 判断删除的评论是主评论还是子评论
      if (subCommentIndex === -1) {
        this.listData.splice(commentIndex, 1)
      } else {
        this.listData[commentIndex].comment.splice(subCommentIndex, 1)
        // 减少评论数量
        this.listData[commentIndex].commentCount--
      }
    },

    _findCommentIdInComment(id) {
      let indexValue = -1
      let subIndexValue = -1
      this.listData.forEach((item, index) => {
        item.comment.forEach((_, subIndex) => {
          if (_.id === id) {
            indexValue = index
            subIndexValue = subIndex
          }
        })
      })
      return { index: indexValue, subIndex: subIndexValue }
    },
  },
}
</script>

<template>
  <div class="comment-list">
    <!--    评论列表-->
    <div v-for="(item, index) in listData" :key="index" class="comment-item">
      <img :src="item.avatar" alt="" class="avatar" />
      <div class="content" @click.stop="replyClickHandle(item)">
        <div class="nickname">{{ item.nickname }}</div>
        <div class="text">{{ item.content }}</div>
        <CommentBottomOperation
          :active-like-icon="activeLikeIcon"
          :active-like-icon-color="activeLikeIconColor"
          :data="{
            date: item.date,
            likeActive: item.likeActive,
            likeCount: item.likeCount,
          }"
          :like-icon="likeIcon"
          :like-icon-color="likeIconColor"
          :show-delete="item.allowDelete"
          :show-like="showLike"
          @delete="$emit('delete', item.id)"
          @like="likeClickHandle(item.id, index)"
        />

        <!--        回复评论列表-->
        <div
          :style="{
            height: `${item.hidden ? '0px' : 'auto'}`,
          }"
          class="overflow-hidden"
        >
          <div v-for="(n, i) in item.comment" :key="i" class="reply">
            <img :src="n.avatar" alt="" class="avatar" />
            <div class="content" @click.stop="replyClickHandle(n)">
              <div class="nickname">
                {{ n.nickname }}
                <span v-if="n.authorNickname">▶ {{ n.authorNickname }}</span>
              </div>
              <div class="text">{{ n.content }}</div>
              <CommentBottomOperation
                :active-like-icon="activeLikeIcon"
                :active-like-icon-color="activeLikeIconColor"
                :data="{
                  date: n.date,
                  likeActive: n.likeActive,
                  likeCount: n.likeCount,
                }"
                :like-icon="likeIcon"
                :like-icon-color="likeIconColor"
                :show-delete="n.allowDelete"
                :show-like="showLike"
                @delete="$emit('delete', n.id)"
                @like="likeClickHandle(item.id, index, i)"
              />
            </div>
          </div>
        </div>

        <!--        加载回复评论-->
        <div
          v-if="item.commentCount > 0"
          class="more"
          @click.stop="replyOperationClickHandle(item.id)"
        >
          <div class="h-px w-5 bg-neutral-100"></div>
          <span v-if="!item.comment.length || item.hidden">查看{{ item.commentCount }}条回复</span>
          <span v-else-if="item.commentCount > item.comment.length">加载更多回复</span>
          <span v-else>收起</span>
          <van-icon
            :name="`arrow-${
              item.commentCount > item.comment.length || item.hidden ? 'down' : 'up'
            }`"
            color="#999"
            size="12px"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.comment-list {
  display: flex;
  padding: 16px;
  flex-direction: column;
  gap: 12px;
}

.avatar {
  border-radius: 50%;
  width: 32px;
  height: 32px;
}

.comment-item {
  display: flex;
  gap: 12px;
}

.content {
  flex: 1;
}

.nickname {
  font-size: 14px;
  color: #999999;
}

.text {
  margin-top: 4px;
}

.replies {
  display: flex;
  margin-top: 8px;
  flex-direction: column;
  gap: 8px;
}

.reply {
  display: flex;
  margin-top: 12px;
  column-gap: 8px;
}

.more {
  margin-top: 6px;
  font-size: 12px;
  color: #999999;
  cursor: pointer;
}
</style>
