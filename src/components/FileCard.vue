<script>
import { extractFileNameAndExtension, getFileIconFromUrl, previewFile } from '@/utils'

export default {
  name: 'FileCard',
  props: {
    // 文件地址
    url: {
      type: String,
      required: true,
    },
    // 文件名
    name: {
      type: String,
    },
    // 是否开启预览
    isView: {
      type: Boolean,
      default: true,
    },
    // 卡片背景颜色
    bgColor: {
      type: String,
      default: '#F4F8FB',
    },
  },
  computed: {
    fileName() {
      const { fileName, extension } = extractFileNameAndExtension(this.url)
      return `${fileName}.${extension}`
    },
  },
  methods: {
    getFileIconFromUrl,

    // 文件卡片点击事件（预览文件）
    handleFileCardClick() {
      if (this.isView) {
        previewFile(this.url)
      }
    },
  },
}
</script>

<template>
  <div
    :style="{ backgroundColor: bgColor }"
    class="flex items-center gap-x-2 overflow-hidden rounded-lg px-3 py-4"
    @click="handleFileCardClick"
  >
    <img :src="getFileIconFromUrl(url)" alt="" class="size-10 shrink-0" />
    <p class="line-clamp-2 text-base text-[#666]">
      {{ name || fileName }}
    </p>
  </div>
</template>
