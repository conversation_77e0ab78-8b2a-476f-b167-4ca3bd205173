<script>
export default {
  name: 'Navbar',
  props: {
    fixed: {
      type: Boolean,
      default: true,
    },
    placeholder: {
      type: Boolean,
      default: true,
    },
    safeAreaInsetTop: {
      type: Boolean,
      default: true,
    },
    color: {
      type: String,
      default: '#333',
    },
    bgColor: {
      type: String,
      default: '#fff',
    },
    isBack: {
      type: Boolean,
      default: true,
    },
    leftArrow: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    handleLeftClick() {
      if (this.isBack) {
        this.$router.back()
      }
    },
  },
}
</script>

<template>
  <van-nav-bar
    :border="false"
    :fixed="fixed"
    :left-arrow="leftArrow"
    :placeholder="placeholder"
    :safe-area-inset-top="safeAreaInsetTop"
    v-bind="$attrs"
    :z-index="9999"
    @click-left="handleLeftClick"
  >
    <template #left>
      <slot name="left"></slot>
    </template>
    <template #title>
      <slot name="title"></slot>
    </template>
    <template #right>
      <slot name="right"></slot>
    </template>
  </van-nav-bar>
</template>

<style lang="scss" scoped>
:deep(.van-nav-bar) {
  background-color: v-bind('bgColor');
}

:deep(.van-nav-bar .van-icon) {
  color: v-bind('color') !important;
}

:deep(.van-nav-bar__title) {
  color: v-bind('color') !important;
}
</style>
