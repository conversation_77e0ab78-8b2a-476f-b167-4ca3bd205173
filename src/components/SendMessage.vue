<script>
export default {
  props: {
    focus: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: '善语结善缘，恶言伤人心',
    },
  },
  data() {
    return {
      inputValue: '',
    }
  },
  computed: {
    disabled() {
      return !this.inputValue
    },
  },
  watch: {
    focus(val) {
      if (val) {
        this.$refs.textareaRef?.focus()
      }
    },
  },
  methods: {
    handleInput(e) {
      this.inputValue = e.target.value
    },
    handleFocus() {
      this.$emit('update:focus', true)
    },
    handleBlur() {
      this.$emit('update:focus', false)
      this.$emit('blur')
    },
    handleSend() {
      if (!this.inputValue) return
      this.$emit('send', this.inputValue)
      this.inputValue = ''
      this.$emit('update:focus', false)
    },
  },
}
</script>

<template>
  <div class="flex w-full items-end gap-x-2 bg-white">
    <div id="chat-input-bar" class="flex-1 overflow-hidden" style="height: 64px">
      <textarea
        ref="textareaRef"
        :placeholder="placeholder"
        :value="inputValue"
        class="w-full rounded bg-neutral-100 p-2"
        @blur="handleBlur"
        @focus="handleFocus"
        @input="handleInput"
        @keyup.enter="handleSend"
      ></textarea>
    </div>
    <div class="w-[72px]">
      <button
        :disabled="disabled"
        class="mt-[-4px] h-8 w-full rounded-xl bg-blue-500 text-white shadow"
        @click="handleSend"
      >
        发送
      </button>
    </div>
  </div>
</template>
