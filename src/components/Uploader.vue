<script>
import { uploadFileApi } from '@/services/api/common/service'
import { baseUrl, getFullAssetsUrl } from '@/utils'

export default {
  name: 'Uploader',
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: {
    modelValue: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      fileList: [],
    }
  },
  watch: {
    // modelValue: {
    //   deep: true,
    //   immediate: true,
    //   handler() {
    //     this.fileList = this.modelValue.map((item) => ({
    //       url: getFullAssetsUrl(item),
    //       status: 'done',
    //     }))
    //   },
    // },
    fileList: {
      deep: true,
      handler(newVal) {
        this.$emit(
          'update:modelValue',
          newVal.map((item) => {
            console.log('🚀 ~  ~ item: ', item)
            // 修复正则表达式语法错误
            // 原来: new RegExp(baseUrl, '^') - 错误的语法
            // 修复: new RegExp('^' + baseUrl) - 正确的语法
            const baseUrlPattern = new RegExp('^' + baseUrl, '')
            return item.url.replace(baseUrlPattern, '')
          })
        )
      },
    },
  },
  methods: {
    async afterRead(file) {
      file.status = 'uploading'
      file.message = '上传中...'
      const actualFile = file.file
      const formData = new FormData()
      formData.append('file', actualFile)
      const res = await uploadFileApi(formData)

      file.url = getFullAssetsUrl(res.file.url)
      file.status = 'done'
    },
  },
}
</script>

<template>
  <van-uploader v-model="fileList" :after-read="afterRead" />
</template>

<style lang="scss" scoped>
:deep(.van-uploader__upload) {
  width: 90px;
  height: 90px;
}

:deep(.van-uploader__preview-image) {
  width: 90px;
  height: 90px;
}
</style>
