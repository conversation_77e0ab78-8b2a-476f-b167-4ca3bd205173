<script>
import { uploadFileApi } from '@/services/api/common/service'
import { getFullAssetsUrl } from '@/utils'

export default {
  name: 'Uploader',
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: {
    modelValue: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      fileList: [],
    }
  },
  methods: {
    async afterRead(file) {
      file.status = 'uploading'
      file.message = '上传中...'
      const actualFile = file.file
      const formData = new FormData()
      formData.append('file', actualFile)
      const res = await uploadFileApi(formData)

      file.url = getFullAssetsUrl(res.file.url)
      file.status = 'done'
    },
  },
}
</script>

<template>
  <van-uploader v-model="fileList" :after-read="afterRead" />
</template>

<style lang="scss" scoped>
:deep(.van-uploader__upload) {
  width: 100px;
  height: 100px;
}

:deep(.van-uploader__preview-image) {
  width: 100px;
  height: 100px;
}
</style>
