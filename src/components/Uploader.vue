<script>
export default {
  name: 'Uploader',
  props: {
    modelValue: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    fileList: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      },
    },
  },
  methods: {
    async afterRead(file) {
      // console.log('🚀 ~ afterRead ~ file: ', file)
      // console.log('🚀 ~ afterRead ~ file: ', file.content)
      // const formData = new FormData()
      // formData.append('file', file.content)
      // const res = await uploadFileApi(formData)
      // console.log('🚀 ~ afterRead ~ res: ', res)
      // // console.log('🚀 ~ afterRead ~ file: ', file)
    },
  },
}
</script>

<template>
  <van-uploader v-model="fileList" :after-read="afterRead" accept="*" />
</template>
