<script>
export default {
  name: 'UploaderDebug',
  props: {
    modelValue: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    fileList: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      },
    },
  },
  methods: {
    /**
     * 详细的调试版本 - 帮助定位 file.file 为空的问题
     */
    async afterRead(file) {
      console.group('🔍 Uploader Debug - afterRead')
      
      // 基本信息
      console.log('📄 原始 file 参数:', file)
      console.log('📄 file 类型:', typeof file)
      console.log('📄 file 构造函数:', file?.constructor?.name)
      console.log('📄 是否为数组:', Array.isArray(file))
      console.log('📄 是否为 File 对象:', file instanceof File)
      
      // 如果是对象，打印所有属性
      if (file && typeof file === 'object' && !Array.isArray(file)) {
        console.log('📄 对象的所有属性:')
        Object.keys(file).forEach(key => {
          const value = file[key]
          console.log(`  - ${key}:`, value, `(${typeof value})`)
          
          // 如果属性值是对象，进一步检查
          if (value && typeof value === 'object') {
            console.log(`    └─ ${key} 的类型:`, value.constructor?.name)
            if (value instanceof File) {
              console.log(`    └─ ${key} 是 File 对象! ✅`)
              console.log(`    └─ 文件名: ${value.name}`)
              console.log(`    └─ 文件大小: ${value.size} bytes`)
              console.log(`    └─ 文件类型: ${value.type}`)
            }
          }
        })
      }
      
      // 如果是数组，检查数组内容
      if (Array.isArray(file)) {
        console.log('📄 数组长度:', file.length)
        file.forEach((item, index) => {
          console.log(`📄 数组[${index}]:`, item)
          console.log(`📄 数组[${index}] 类型:`, typeof item)
          console.log(`📄 数组[${index}] 构造函数:`, item?.constructor?.name)
        })
      }
      
      // 尝试不同的方式获取文件
      console.log('🔍 尝试获取文件对象:')
      
      const attempts = [
        { name: 'file.file', getter: () => file.file },
        { name: 'file.content', getter: () => file.content },
        { name: 'file.raw', getter: () => file.raw },
        { name: 'file.originFile', getter: () => file.originFile },
        { name: 'file 本身', getter: () => file },
        { name: 'file[0] (如果是数组)', getter: () => Array.isArray(file) ? file[0] : null },
      ]
      
      let actualFile = null
      
      attempts.forEach(({ name, getter }) => {
        try {
          const result = getter()
          console.log(`  - ${name}:`, result)
          
          if (result instanceof File) {
            console.log(`    ✅ ${name} 是 File 对象!`)
            console.log(`    └─ 文件名: ${result.name}`)
            console.log(`    └─ 文件大小: ${result.size} bytes`)
            console.log(`    └─ 文件类型: ${result.type}`)
            
            if (!actualFile) {
              actualFile = result
              console.log(`    🎯 使用 ${name} 作为实际文件对象`)
            }
          } else if (result && typeof result === 'object') {
            console.log(`    ⚠️ ${name} 是对象但不是 File:`, result.constructor?.name)
          } else {
            console.log(`    ❌ ${name} 不可用:`, typeof result)
          }
        } catch (error) {
          console.log(`    ❌ ${name} 获取失败:`, error.message)
        }
      })
      
      console.groupEnd()
      
      // 如果找到了文件对象，进行上传
      if (actualFile) {
        console.log('🚀 开始上传文件:', actualFile.name)
        await this.uploadFile(actualFile)
      } else {
        console.error('❌ 无法获取文件对象，请检查 Vant Uploader 版本和配置')
        this.$toast.fail('无法获取文件，请重试')
      }
    },
    
    /**
     * 上传文件
     */
    async uploadFile(file) {
      try {
        const formData = new FormData()
        formData.append('file', file)
        
        console.log('📤 准备上传:', {
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type,
          formData: formData
        })
        
        // TODO: 调用实际的上传 API
        // const result = await uploadFileApi(formData)
        
        // 模拟上传
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        console.log('✅ 文件上传成功')
        this.$toast.success('文件上传成功')
        
      } catch (error) {
        console.error('❌ 文件上传失败:', error)
        this.$toast.fail('文件上传失败')
      }
    },
    
    /**
     * 上传前的回调
     */
    beforeRead(file) {
      console.log('📋 beforeRead:', file)
      return true
    },
    
    /**
     * 删除文件的回调
     */
    onDelete(file, detail) {
      console.log('🗑️ onDelete:', { file, detail })
    },
    
    /**
     * 点击预览图的回调
     */
    onClickPreview(file, detail) {
      console.log('👁️ onClickPreview:', { file, detail })
    }
  },
}
</script>

<template>
  <div class="uploader-debug">
    <h3>🔍 Uploader 调试版本</h3>
    <p class="text-sm text-gray-500 mb-4">
      请打开浏览器控制台查看详细的调试信息
    </p>
    
    <van-uploader
      v-model="fileList"
      :after-read="afterRead"
      :before-read="beforeRead"
      :on-delete="onDelete"
      :on-click-preview="onClickPreview"
      multiple
      :max-count="5"
      :max-size="10 * 1024 * 1024"
      upload-text="选择文件"
    />
    
    <div v-if="fileList.length" class="mt-4">
      <h4>当前文件列表:</h4>
      <pre class="text-xs bg-gray-100 p-2 rounded">{{ JSON.stringify(fileList, null, 2) }}</pre>
    </div>
  </div>
</template>

<style scoped>
.uploader-debug {
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}
</style>
