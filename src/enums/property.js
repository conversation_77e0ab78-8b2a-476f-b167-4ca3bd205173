export const IsSelfEnum = {
  NO: 0,
  YES: 1,
}

/**
 * @description 是否热门枚举
 * @enum {number}
 * @readonly
 */
export const IsHotEnum = {
  NO: 0,
  YES: 1,
}

/**
 * @description 质疑状态枚举
 * @enum {number}
 * @readonly
 */
export const QuestionStatusEnum = {
  /** 处理中 */
  Processing: 1,
  /** 待核验 */
  PendingVerification: 2,
  /** 已完结 */
  Completed: 3,
}

/**
 * @description 质疑处理状态枚举
 * @enum {number}
 * @readonly
 */
export const QuestionProcessStatusEnum = {
  /** 已上报 */
  Reported: 0,
  /** 待处理 */
  Pending: 1,
  /** 已催办 */
  Urged: 2,
  /** 已处理 */
  Processed: 3,
  /** 待核验 */
  PendingVerification: 4,
  /** 已完结 */
  Completed: 5,
}

/**
 * @description 账单类型枚举
 * @enum {number}
 * @readonly
 */
export const BankBillTypeEnum = {
  /** 全部账单 */
  ALL: 0,
  /** 收入账单 */
  INCOME: 1,
  /** 支出账单 */
  EXPENSE: 2,
}

/**
 * @description 投票状态枚举
 * @enum {number}
 * @readonly
 */
export const VoteStatusEnum = {
  /** 进行中 */
  ONGOING: 1,
  /** 已结束 */
  FINISHED: 2,
}

export const VoteAnonymityEnum = {
  /** 非匿名 */
  NOT_ANONYMOUS: 0,
  /** 匿名 */
  ANONYMOUS: 1,
}

export const DirectionEnum = {
  /** 点赞 */
  UP: 1,
  /** 点踩 */
  DOWN: 2,
}
