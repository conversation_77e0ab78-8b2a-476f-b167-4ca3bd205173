<script>
import { getFullAssetsUrl } from '@/utils'

export default {
  data() {
    return {
      activeIndex: 0,
      tabbarList: [
        {
          name: '首页',
          icon: getFullAssetsUrl('/wxapp/property/tabbar_home.png'),
          activeIcon: getFullAssetsUrl('/wxapp/property/tabbar_home_active.png'),
          path: '/property/home',
        },
        {
          name: '报事报修',
          icon: getFullAssetsUrl('/wxapp/property/tabbar_report.png'),
          activeIcon: getFullAssetsUrl('/wxapp/property/tabbar_report_active.png'),
          path: 'property/report',
        },
        {
          name: '我的',
          icon: getFullAssetsUrl('/wxapp/property/tabbar_my.png'),
          activeIcon: getFullAssetsUrl('/wxapp/property/tabbar_my_active.png'),
          path: 'property/my',
        },
      ],
    }
  },
}
</script>

<template>
  <div class="flex h-screen flex-col">
    <div class="flex-1">
      <router-view></router-view>
    </div>
    <van-tabbar v-model="activeIndex" safe-area-inset-bottom placeholder route :border="false">
      <van-tabbar-item v-for="(item, index) in tabbarList" :key="index" :to="item.path">
        <span>{{ item.name }}</span>
        <template #icon="props">
          <img
            style="width: 30px; height: 30px"
            :src="props.active ? item.activeIcon : item.icon"
          />
        </template>
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>
