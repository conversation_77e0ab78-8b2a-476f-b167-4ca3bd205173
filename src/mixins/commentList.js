import { convertToTree, formatDate, getFullAssetsUrl } from '@/utils'
import {
  deleteCommentApi,
  getCommentListApi,
  giveCommentLikeApi,
  publicCommentApi,
} from '@/services/api/property/comment'
import createListRefresh from '@/mixins/listRefresh'

/**
 * 评论列表相关功能的 mixin
 * @param {Object} config - 配置项
 * @param {Function} config.getQuery - 评论参数
 */
export default function createCommentList(config) {
  const { getQuery } = config

  return {
    mixins: [
      createListRefresh(getCommentListApi, {
        prefix: 'comment',
      }),
    ],
    data() {
      return {
        showInputBar: false,
        initPlaceholder: '善语结善缘，恶言伤人心',
        commentPlaceholder: '善语结善缘，恶言伤人心',
        keyboardHeight: 0,
        commentParams: { ...getQuery() },
      }
    },

    watch: {
      showInputBar(newVal) {
        if (!newVal) {
          this.commentParams.parent_id = 0
          this.commentPlaceholder = this.initPlaceholder
        }
      },
    },

    methods: {
      _convertCommentList(commentList) {
        return commentList.map((item) => {
          return {
            id: item.id,
            avatar: item.createUser ? getFullAssetsUrl(item.createUser.avatar_url) : '',
            nickname: item.createUser ? item.createUser.realname : '用户已注销',
            date: formatDate(item.create_time, 'MM/DD'),
            content: item.content,
            likeActive: item.liked,
            likeCount: item.like_num,
            disabledReply: false,
            allowDelete: item.is_self,
            commentCount: item.child_count,
            comment: item.children ? this._convertCommentList(item.children) : [],
          }
        })
      },

      onKeyboardHeightChange(res) {
        this.keyboardHeight = res.height
      },

      // 查看更多评论
      async showMoreComment({ id }) {
        const params = { ...this.commentParams, parent_id: id }
        const { data } = await getCommentListApi({ page: 1, limit: 10, ...params })
        if (this.$refs.CommentListRef) {
          this.$refs.CommentListRef.addCommentData(
            id,
            this._convertCommentList(convertToTree(data, id))
          )
        }
      },

      // 回复评论事件的点击
      replyCommentClick({ id, nickname }) {
        if (this.showInputBar) return
        this.commentPlaceholder = `回复@${nickname}`
        this.commentParams.parent_id = id
        this.showInputBar = true
      },

      // 发布｜回复评论
      async replyComment(content) {
        if (!content) return
        const comment = await publicCommentApi({
          ...this.commentParams,
          content,
          images: [],
        })

        // 发布评论
        if (!this.commentParams.parent_id) {
          this.commentRefreshListData()
        }
        // 回复评论
        else {
          this.$refs.CommentListRef.addCommentReply(this.commentParams.parent_id, {
            id: comment.id,
            avatar: getFullAssetsUrl(comment.createUser.avatar_url),
            nickname: comment.createUser.realname,
            date: formatDate(comment.create_time, 'MM/DD'),
            content,
            allowDelete: true,
            disabledReply: false,
          })
        }
      },

      // 点赞｜取消点赞
      async likeComment(id) {
        await giveCommentLikeApi(id)
      },

      // 删除评论
      async deleteComment(id) {
        await deleteCommentApi(id)
        this.$refs.CommentListRef.deleteCommentReply(id)
      },
    },
  }
}
