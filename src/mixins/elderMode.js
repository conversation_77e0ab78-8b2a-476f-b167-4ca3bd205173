import { mapGetters, mapActions } from 'vuex'

export default {
  computed: {
    ...mapGetters('elderMode', [
      'isElderMode',
      'elderModeText',
      'elderModeClass',
      'elderModeClasses',
    ]),
  },
  methods: {
    ...mapActions('elderMode', ['toggleElderMode', 'setElderMode', 'initElderMode']),

    // 获取适老版样式类
    getElderClass(baseClass = '') {
      return this.isElderMode ? `${baseClass} elder-mode` : baseClass
    },

    // 获取CSS变量值
    getCSSVar(varName) {
      return `var(--${varName})`
    },

    // 获取字体大小CSS变量
    getFontSize(size = 'base') {
      return this.getCSSVar(`font-size-${size}`)
    },

    // 获取间距CSS变量
    getSpacing(size = 'base') {
      return this.getCSSVar(`spacing-${size}`)
    },

    // 获取按钮高度CSS变量
    getButtonHeight() {
      return this.getCSSVar('button-height')
    },

    // 获取输入框高度CSS变量
    getInputHeight() {
      return this.getCSSVar('input-height')
    },

    // 获取图标尺寸CSS变量
    getIconSize() {
      return this.getCSSVar('icon-size')
    },

    // 获取卡片内边距CSS变量
    getCardPadding() {
      return this.getCSSVar('card-padding')
    },

    // 获取容器内边距CSS变量
    getContainerPadding() {
      return this.getCSSVar('container-padding')
    },
  },

  mounted() {
    // 组件挂载时初始化适老版状态
    this.initElderMode()
  },
}
