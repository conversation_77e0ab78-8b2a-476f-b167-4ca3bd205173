/**
 * listRefresh mixin 使用示例
 */

import createListRefresh from './listRefresh'
import { getQuestionListApi, getMyQuestionListApi, getOtherQuestionListApi } from '@/services/api'

// ========== 新版本用法（推荐） ==========

// 1. 最简单的用法 - 只传 API 函数
export const simpleListMixin = createListRefresh(getQuestionListApi)

// 2. 传入查询参数函数
export const withQueryMixin = createListRefresh(getQuestionListApi, {
  getQuery() {
    return {
      status: this.currentStatus,
      keyword: this.searchKeyword
    }
  }
})

// 3. 传入自定义数据处理函数
export const withDataHandlerMixin = createListRefresh(getQuestionListApi, {
  getQuery() {
    return { status: this.currentStatus }
  },
  dataHandler(response) {
    return {
      data: response.result.items,
      total: response.result.totalCount
    }
  }
})

// 4. 使用前缀区分多个列表
export const multipleListsMixin = {
  mixins: [
    // 我的质疑列表
    createListRefresh(getMyQuestionListApi, {
      prefix: 'my',
      limit: 15,
      getQuery() {
        return { self: 1, status: this.myStatus }
      }
    }),
    
    // 其他质疑列表
    createListRefresh(getOtherQuestionListApi, {
      prefix: 'other',
      limit: 20,
      getQuery() {
        return { self: 0, status: this.otherStatus }
      },
      dataHandler(response) {
        // 自定义数据处理
        return {
          data: response.data.list,
          total: response.data.count
        }
      }
    })
  ],
  
  data() {
    return {
      myStatus: 1,
      otherStatus: 2
    }
  },
  
  methods: {
    // 刷新我的列表
    refreshMyList() {
      this.myOnRefresh()
    },
    
    // 刷新其他列表
    refreshOtherList() {
      this.otherOnRefresh()
    },
    
    // 加载更多我的列表
    loadMoreMy() {
      this.myFetchListData()
    }
  }
}

// 5. 完整配置示例
export const fullConfigMixin = createListRefresh(getQuestionListApi, {
  // 查询参数函数
  getQuery() {
    return {
      status: this.currentStatus,
      keyword: this.searchKeyword,
      category: this.selectedCategory,
      startDate: this.dateRange.start,
      endDate: this.dateRange.end
    }
  },
  
  // 数据处理函数
  dataHandler(response, vm) {
    // vm 是当前 Vue 实例，可以访问组件的数据和方法
    console.log('当前页码:', vm.page)
    
    // 处理复杂的响应格式
    if (response.code === 200) {
      return {
        data: response.data.records,
        total: response.data.total
      }
    } else {
      // 错误处理
      vm.$toast(response.message)
      return { data: [], total: 0 }
    }
  },
  
  // 属性前缀
  prefix: 'question',
  
  // 每页数量
  limit: 25
})

// ========== 向下兼容旧版本用法 ==========

// 旧版本用法仍然支持
export const oldVersionMixin = createListRefresh(
  getQuestionListApi,
  function() { return { status: this.currentStatus } },  // getQuery
  function(response) { return { data: response.data, total: response.total } },  // dataHandler
  { prefix: 'old', limit: 10 }  // options
)

// ========== 在 Vue 组件中使用 ==========

export default {
  mixins: [
    // 使用单个列表
    createListRefresh(getQuestionListApi, {
      getQuery() {
        return { status: this.currentStatus }
      }
    })
  ],
  
  data() {
    return {
      currentStatus: 1
    }
  },
  
  created() {
    // 组件创建时自动加载数据
    this.fetchListData()
  },
  
  methods: {
    // 切换状态时重新加载
    handleStatusChange(status) {
      this.currentStatus = status
      this.onRefresh()  // 刷新列表
    },
    
    // 搜索
    handleSearch(keyword) {
      this.searchKeyword = keyword
      this.onRefresh()
    }
  }
}
