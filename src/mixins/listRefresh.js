/**
 * 创建列表刷新 mixin
 * @param {Function} fetchApi - 获取数据的 API 函数
 * @param {Function} [getQuery] - 获取查询参数的函数，可选
 * @param {Function} [dataHandler] - 数据处理函数，可选，默认处理 { data, total } 格式
 * @param {Object} [options={}] - 配置选项
 * @param {string} [options.prefix=''] - 属性名前缀，用于区分多个列表
 * @param {number} [options.limit=10] - 每页数量，默认 10
 * @returns {Object} Vue mixin 对象
 */
export default function createListRefresh(fetchApi, getQuery, dataHandler, options = {}) {
  // 处理参数重载：支持 (fetchApi, options) 或 (fetchApi, getQuery, options) 等形式
  if (typeof getQuery === 'object' && getQuery !== null && !Array.isArray(getQuery)) {
    // 第二个参数是 options 对象
    options = getQuery
    getQuery = undefined
    dataHandler = undefined
  } else if (
    typeof dataHandler === 'object' &&
    dataHandler !== null &&
    !Array.isArray(dataHandler)
  ) {
    // 第三个参数是 options 对象
    options = dataHandler
    dataHandler = undefined
  }

  const { prefix = '', limit = 10 } = options

  // 默认的数据处理函数
  const defaultDataHandler = (response) => {
    // 支持多种常见的响应格式
    if (response && typeof response === 'object') {
      // 格式1: { data: [...], total: 100 }
      if (response.data && typeof response.total === 'number') {
        return { data: response.data, total: response.total }
      }
      // 格式2: { list: [...], total: 100 }
      if (response.list && typeof response.total === 'number') {
        return { data: response.list, total: response.total }
      }
      // 格式3: { items: [...], count: 100 }
      if (response.items && typeof response.count === 'number') {
        return { data: response.items, total: response.count }
      }
      // 格式4: 直接是数组
      if (Array.isArray(response)) {
        return { data: response, total: response.length }
      }
      // 格式5: { data: [...] } 没有 total
      if (response.data && Array.isArray(response.data)) {
        return { data: response.data, total: response.data.length }
      }
    }
    // 兜底处理
    return { data: [], total: 0 }
  }

  // 使用传入的 dataHandler 或默认的
  const finalDataHandler = dataHandler || defaultDataHandler

  // 生成带前缀的属性名
  const getPropertyName = (name) => {
    return prefix ? `${prefix}${name.charAt(0).toUpperCase() + name.slice(1)}` : name
  }

  const loadingKey = getPropertyName('loading')
  const finishedKey = getPropertyName('finished')
  const refreshingKey = getPropertyName('refreshing')
  const listKey = getPropertyName('list')
  const pageKey = getPropertyName('page')
  const limitKey = getPropertyName('limit')
  const totalKey = getPropertyName('total')
  const fetchMethodKey = getPropertyName('fetchListData')
  const refreshMethodKey = getPropertyName('onRefresh')

  return {
    data() {
      return {
        [loadingKey]: false,
        [finishedKey]: false,
        [refreshingKey]: false,
        [listKey]: [],
        [pageKey]: 1,
        [limitKey]: limit,
        [totalKey]: 0,
      }
    },
    methods: {
      async [fetchMethodKey]() {
        if (this[refreshingKey]) {
          this[listKey] = []
          this[refreshingKey] = false
        }
        const page = this[pageKey]
        const limit = this[limitKey]
        const query = typeof getQuery === 'function' ? getQuery.call(this) : {}

        const response = await fetchApi({
          page,
          limit,
          ...query,
        })
        // 由外部自定义如何提取 data 和 total，或使用默认处理
        const { data, total } = finalDataHandler(response, this)
        this[totalKey] = total
        this[listKey].push(...data)
        this[loadingKey] = false
        if (total <= this[listKey].length) {
          this[finishedKey] = true
        }
        this[pageKey]++
      },
      [refreshMethodKey]() {
        this[finishedKey] = false
        this[refreshingKey] = true
        this[loadingKey] = true
        this[pageKey] = 1
        this[totalKey] = 0
        this[fetchMethodKey]()
      },
    },
  }
}
