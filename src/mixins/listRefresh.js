export default function createListRefresh(fetchApi, getQuery, dataHandler) {
  return {
    data() {
      return {
        loading: false,
        finished: false,
        refreshing: false,
        list: [],
        page: 1,
        limit: 10,
        total: 0,
      }
    },
    methods: {
      async fetchListData() {
        if (this.refreshing) {
          this.list = []
          this.refreshing = false
        }
        const page = this.page
        const limit = this.limit
        const query = typeof getQuery === 'function' ? getQuery.call(this) : {}

        const response = await fetchApi({
          page,
          limit,
          ...query,
        })
        // 由外部自定义如何提取 data 和 total
        const { data, total } = dataHandler(response, this)
        this.total = total
        this.list.push(...data)
        this.loading = false
        if (total <= this.list.length) {
          this.finished = true
        }
        this.page++
      },
      onRefresh() {
        this.finished = false
        this.refreshing = true
        this.loading = true
        this.page = 1
        this.total = 0
        this.fetchListData()
      },
    },
  }
}
