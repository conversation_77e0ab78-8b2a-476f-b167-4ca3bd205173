import { hexOrRgbToRgba } from '@/utils'

/**
 * @description 导航栏背景色渐变 mixin
 * @param {string} bgColor 导航栏背景色（默认值为 #fff）
 * @returns {Object} Vue mixin 对象
 */
export default function navbarBgColor(bgColor = '#fff') {
  return {
    data() {
      return {
        NAVBAR_ID: 'navbar',
        PAGE_CONTAINER_ID: 'page_container',
        navBarTop: 0,
        navBarChangeBaseLineHeight: 0,
        navBarBackgroundColor: '',
      }
    },
    mounted() {
      this.$nextTick(() => {
        this.initNavBarRectInfo()
        this.updateNavBarRectInfo()
      })
      window.addEventListener('scroll', this.updateNavBarRectInfo)
    },
    destroyed() {
      window.removeEventListener('scroll', this.updateNavBarRectInfo)
    },
    methods: {
      initNavBarRectInfo() {
        const navBar = document.getElementById(this.NAVBAR_ID)
        const pageContainer = document.getElementById(this.PAGE_CONTAINER_ID)

        if (!navBar || !pageContainer) return

        const navRect = navBar.getBoundingClientRect()
        const pageRect = pageContainer.getBoundingClientRect()

        this.navBarTop = navRect.top
        this.navBarChangeBaseLineHeight = pageRect.top - navRect.top
        this.navBarBackgroundColor = hexOrRgbToRgba(bgColor, 0)
      },
      updateNavBarRectInfo() {
        const pageContainer = document.getElementById(this.PAGE_CONTAINER_ID)
        if (!pageContainer) return

        const rect = pageContainer.getBoundingClientRect()
        const top = rect.top || 0
        const differHeight = top - this.navBarTop
        let opacity = differHeight / this.navBarChangeBaseLineHeight
        if (opacity > 1) opacity = 1
        if (opacity < 0) opacity = 0

        this.navBarBackgroundColor = hexOrRgbToRgba(bgColor, 1 - opacity)
      },
    },
  }
}
