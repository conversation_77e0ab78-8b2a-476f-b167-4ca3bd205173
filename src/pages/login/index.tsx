import React from 'react'
import { Tabs, Input, Button, Checkbox, Form } from 'antd'
import { UserOutlined, LockOutlined } from '@ant-design/icons'
import { Link } from 'react-router'
import './index.scss'

const Login: React.FunctionComponent = () => {
  const [form] = Form.useForm()

  const onFinish = (values: any) => {
    console.log('Success:', values)
  }

  return (
    <div className="h-screen flex items-center justify-center bg-gradient-to-r from-blue-50 to-indigo-50">
      <div className="max-w-md w-full p-8 pt-4 bg-white rounded-xl shadow-lg">
        <Tabs
          size="large"
          defaultActiveKey="account"
          centered
          items={[
            {
              key: 'account',
              label: '账号登录',
              children: (
                <Form
                  size="large"
                  form={form}
                  name="login"
                  onFinish={onFinish}
                  className="space-y-5 pt-4"
                >
                  <Form.Item
                    name="username"
                    rules={[{ required: true, message: '请输入用户名/Email/手机' }]}
                  >
                    <Input
                      prefix={<UserOutlined className="text-gray-400" />}
                      placeholder="用户名/Email/手机"
                      className="text-sm rounded-lg"
                    />
                  </Form.Item>

                  <Form.Item name="password" rules={[{ required: true, message: '请输入密码' }]}>
                    <Input.Password
                      prefix={<LockOutlined className="text-gray-400" />}
                      placeholder="请输入登录密码"
                      className="text-sm rounded-lg"
                    />
                  </Form.Item>

                  <div className="flex justify-between items-center mb-4 text-xs">
                    <Form.Item name="remember" valuePropName="checked" noStyle>
                      <Checkbox className="text-xs">记住账号密码</Checkbox>
                    </Form.Item>
                    <Link to="/forgot-password" className="hover:text-blue-500">
                      忘记密码？
                    </Link>
                  </div>

                  <Button type="primary" htmlType="submit" className="text-sm" block>
                    登录
                  </Button>

                  <div className="text-center mt-4">
                    <span className="text-sm">还没有账号？</span>
                    <Link to="/register" className="text-blue-500 ml-2 text-sm">
                      立即注册
                    </Link>
                  </div>
                </Form>
              ),
            },
            {
              key: 'code',
              label: '验证码登录',
              children: (
                <Form
                  form={form}
                  name="code-login"
                  onFinish={onFinish}
                  className="space-y-5 pt-4"
                  size="large"
                >
                  <Form.Item name="phone" rules={[{ required: true, message: '请输入手机号' }]}>
                    <Input
                      prefix={<UserOutlined className="text-gray-400" />}
                      placeholder="请输入手机号"
                      className="text-sm rounded-lg"
                    />
                  </Form.Item>

                  <Form.Item name="code" rules={[{ required: true, message: '请输入验证码' }]}>
                    <div className="flex space-x-3">
                      <Input placeholder="请输入验证码" className="text-sm rounded-lg" />
                      <Button type="primary" ghost className="text-sm min-w-[100px]">
                        获取验证码
                      </Button>
                    </div>
                  </Form.Item>

                  <Button type="primary" htmlType="submit" block className="text-sm">
                    登录
                  </Button>
                </Form>
              ),
            },
          ]}
        />
      </div>
    </div>
  )
}

export default Login
