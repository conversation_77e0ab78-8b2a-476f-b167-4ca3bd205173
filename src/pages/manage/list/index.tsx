import React, { useEffect, useState } from 'react'
import { useImmer } from 'use-immer'
import { useSearchParams } from 'react-router'
import { useRequest } from 'ahooks'
import { KEYWORD_KEY, PAGE_SIZE_VALUE } from '@/constant'
import { getQuestionListApi } from '@/services/api/question'
import type { QuestionItem } from '@/services/api/types'
import { Divider, Empty, Spin, Typography } from 'antd'
import Search from '@/components/Search'
import QuestionCard from '@/components/QuestionCard'
import InfiniteScroll from 'react-infinite-scroll-component'

const ManageList: React.FunctionComponent = () => {
  const { Title } = Typography

  const [searchParams] = useSearchParams()
  const keyword = searchParams.get(KEYWORD_KEY) || ''

  const [page, setPage] = useState(1)
  const [list, updateList] = useImmer<QuestionItem[]>([])
  const [total, setTotal] = useState(0)

  const hasMore = total > list.length

  const { loading, run: loadMoreData } = useRequest(
    () => getQuestionListApi({ page, pageSize: PAGE_SIZE_VALUE, keyword }),
    {
      manual: true,
      onSuccess: result => {
        const { list: questionList, total } = result
        updateList(draft => {
          draft.push(...questionList)
        })
        setTotal(total)
        setPage(page + 1)
      },
    }
  )

  const resetData = () => {
    updateList([])
    setTotal(0)
    setPage(1)
  }

  useEffect(() => {
    resetData()
  }, [searchParams])

  useEffect(() => {
    if (page === 1) {
      loadMoreData()
    }
  }, [page])

  // const removeQuestion = (id: string | number) => {
  //   setList(
  //     produce(draft => {
  //       const index = draft.findIndex(item => item.id === id)
  //       draft.splice(index, 1)
  //     })
  //   )
  // }

  // const addQuestion = () => {
  //   setList(
  //     produce(draft => {
  //       draft.push({
  //         id: draft.length + 1,
  //         title: `调查问卷${draft.length + 1}`,
  //         isPublished: false,
  //         isStar: false,
  //         answerCount: 0,
  //         createdAt: new Date().toISOString(),
  //       })
  //     })
  //   )
  // }

  // const publishQuestion = (id: string | number) => {
  //   setList(
  //     produce(draft => {
  //       const item = draft.find(item => item.id === id)
  //       if (item) {
  //         item.isPublished = true
  //       }
  //     })
  //   )
  // }

  return (
    <div className="h-full flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <Title level={4}>我的问卷</Title>
        <Search />
      </div>
      {loading && !list.length ? (
        <div className="flex justify-center">
          <Spin></Spin>
        </div>
      ) : list.length ? (
        <div id="scrollContainer" className="flex-1 overflow-y-auto">
          <InfiniteScroll
            scrollableTarget="scrollContainer"
            next={loadMoreData}
            hasMore={hasMore}
            dataLength={list.length}
            loader={
              <Spin tip="Loading">
                <div className="flex items-center justify-center pt-20"></div>
              </Spin>
            }
            endMessage={<Divider>没有更多数据～</Divider>}
          >
            <div className="flex flex-col gap-y-4 p-1">
              {list.map((item, index) => (
                <QuestionCard key={item.id} {...item} index={index + 1}></QuestionCard>
              ))}
            </div>
          </InfiniteScroll>
        </div>
      ) : (
        <div className="flex justify-center items-center h-full">
          <Empty description="暂无数据" />
        </div>
      )}
    </div>
  )
}

export default ManageList
