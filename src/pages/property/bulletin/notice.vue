<script>
import { formatDate, getFullAssetsUrl } from '@/utils'
import { getNoticeDetailApi } from '@/services/api/property/bulletin'
import createCommentList from '@/mixins/commentList'
import Navbar from '@/components/Navbar.vue'
import FileCard from '@/components/FileCard.vue'
import CommentList from '@/components/Comment/CommentList.vue'

export default {
  components: { CommentList, Navbar, FileCard },
  mixins: [
    createCommentList(function () {
      const data_id = this.id
      return {
        data_id,
        parent_id: 0,
        topic: 'notice',
      }
    }),
  ],
  data() {
    return {
      id: this.$route.params.id,
      noticeDetail: null,
    }
  },
  beforeMount() {
    this.fetchNoticeDetailData()
  },
  methods: {
    getFullAssetsUrl,
    formatDate,

    async fetchNoticeDetailData() {
      this.noticeDetail = await getNoticeDetailApi(this.id)
    },
  },
}
</script>

<template>
  <div class="min-h-screen bg-white p-3.5">
    <Navbar title="通知公告"></Navbar>

    <div v-if="noticeDetail" class="flex flex-col gap-y-2 pb-6">
      <div class="text-justify text-base font-medium">
        {{ noticeDetail.title }}
      </div>
      <div class="flex justify-between">
        <span>
          {{ noticeDetail.community_name }}
        </span>
        <span class="text-sm text-[#999]">
          {{ formatDate(noticeDetail.create_time) }}
        </span>
      </div>

      <div>
        <div class="text-justify">
          {{ noticeDetail.description }}
        </div>
        <img
          v-for="(item, index) in noticeDetail.images"
          :key="index"
          :src="getFullAssetsUrl(item)"
          alt=""
          class="block w-full object-contain"
        />
      </div>

      <div class="mt-2 flex flex-col gap-y-2">
        <FileCard
          v-for="(file, index) in noticeDetail.files"
          :key="index"
          :url="getFullAssetsUrl(file)"
        />
      </div>
      <div class="flex justify-between text-[#999]">
        <div>阅读 {{ noticeDetail.view_num }}</div>
        <div>点赞 {{ noticeDetail.like_num }}</div>
      </div>
    </div>

    <div class="-mx-3.5 h-2 bg-neutral-100"></div>

    <!--    评论-->
    <van-list v-model="commentLoading" :finished="commentFinished" @load="commentFetchListData">
      <van-skeleton :loading="commentLoading" :row="4" avatar>
        <CommentList
          v-if="commentList.length"
          ref="CommentListRef"
          :data="commentList"
          :show-like="false"
          @delete="deleteComment"
          @like="likeComment"
          @reply="replyCommentClick"
          @show-more="showMoreComment"
        />
        <van-empty v-else description="暂无评论" />
      </van-skeleton>
    </van-list>
  </div>
</template>
