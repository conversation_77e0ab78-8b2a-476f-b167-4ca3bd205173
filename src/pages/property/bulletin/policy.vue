<script>
import { getFullAssetsUrl, isEmpty } from '@/utils'
import { getPolicyListApi } from '@/services/api/property/bulletin'
import navbarBgColor from '@/mixins/navbarBgColor'
import createListRefresh from '@/mixins/listRefresh'
import Navbar from '@/components/Navbar.vue'
import FileCard from '@/components/FileCard.vue'
import Iconfont from '@/components/Iconfont.vue'

export default {
  components: { Navbar, FileCard, Iconfont },
  mixins: [navbarBgColor('#fff'), createListRefresh(getPolicyListApi)],
  data() {
    return {}
  },
  methods: {
    isEmpty,
    getFullAssetsUrl,
  },
}
</script>

<template>
  <div class="size-full">
    <Navbar :id="NAVBAR_ID" :bg-color="navBarBackgroundColor" title="政策法规"> </Navbar>
    <div
      :style="{
        backgroundImage: `url(${getFullAssetsUrl('/wxapp/property/policy_banner.png')})`,
        backgroundSize: '100% 100%',
        backgroundRepeat: 'no-repeat',
      }"
      class="z-1 fixed left-0 top-0 h-[155px] w-full"
    >
      <div class="absolute bottom-8 left-0 pl-8 font-['YouSheBiaoTiHei']">
        <div class="mb-2 text-lg font-bold">
          <span class="text-black"> 服务大众 </span>
          <span class="text-[#4787FF]"> 共创美好 </span>
        </div>
        <div class="text-xs text-[#5F6E86]">关注政策法规，共建美丽家园</div>
      </div>
    </div>
    <van-list
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了～"
      @load="fetchListData"
    >
      <div :id="PAGE_CONTAINER_ID" class="z-2 pb-0! relative mt-20 flex flex-col gap-y-2 p-3.5">
        <div
          v-for="(item, index) in list"
          :key="index"
          class="flex flex-col gap-y-2 overflow-hidden rounded-lg bg-white p-3.5"
        >
          <div class="text-base font-medium">
            {{ item.title }}
          </div>
          <FileCard v-if="!isEmpty(item.files)" :url="getFullAssetsUrl(item.files[0])" />
          <div class="flex items-center gap-x-3 text-sm">
            <span class="text-xs"> {{ item.create_time }} 发布 </span>
            <div class="ml-auto text-[#888889]">
              <Iconfont name="yanjing1" size="14px" />
              <span class="ml-1"> 11234 </span>
            </div>
          </div>
        </div>
      </div>
    </van-list>

    <div class="fixed inset-x-0 bottom-5 mx-5 flex h-10 items-center rounded bg-black/60 pl-4">
      <img
        :src="getFullAssetsUrl('/wxapp/property/ai_icon.gif')"
        alt=""
        class="z-99 relative bottom-3 mr-2 h-[66px] w-[50px]"
      />
      <span class="font-medium text-white"> H i～有什么需要，可以问小嘉！ </span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.header {
  background: linear-gradient(90deg, #6592fc 0%, rgba(101, 146, 252, 0.84) 100%);
}
</style>
