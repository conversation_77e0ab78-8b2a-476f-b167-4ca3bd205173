<script>
import DOMPurify from 'dompurify'
import { formatDate, getFullAssetsUrl } from '@/utils'
import { getResolutionDetailApi } from '@/services/api/property/bulletin'
import Navbar from '@/components/Navbar.vue'
import FileCard from '@/components/FileCard.vue'

export default {
  components: {
    Navbar,
    FileCard,
  },
  data() {
    return {
      id: this.$route.params.id,
      commDeciDetail: null,
    }
  },
  computed: {
    descriptionHtml() {
      return DOMPurify.sanitize(this.commDeciDetail?.description.replace(/,/g, ''))
    },
  },
  created() {
    this.fetchNoticeDetailData()
  },
  methods: {
    getFullAssetsUrl,
    formatDate,
    async fetchNoticeDetailData() {
      this.commDeciDetail = await getResolutionDetailApi(this.id)
      console.log(this.commDeciDetail, '-commDeciDetail')
    },
  },
}
</script>

<template>
  <div class="min-h-screen bg-white">
    <Navbar title="公示公告"></Navbar>
    <div v-if="commDeciDetail" class="flex flex-col gap-y-2 bg-white p-3.5">
      <div class="text-justify text-base font-bold">
        {{ commDeciDetail?.title }}
      </div>
      <div class="text-placeholder mt-2 flex justify-between text-sm text-[#999]">
        <div>
          {{ formatDate(commDeciDetail?.create_time) }} {{ commDeciDetail?.publisher_name }}
        </div>
        <div>阅读 {{ commDeciDetail?.view_num }}</div>
      </div>
      <div>
        <img
          v-for="(item, index) in commDeciDetail?.images"
          :key="index"
          :src="getFullAssetsUrl(item)"
          alt=""
          class="block w-full"
          @click="previewImage(index)"
        />
      </div>
      <div class="mt-4" v-html="descriptionHtml" />
      <div class="mt-2 flex flex-col gap-y-2">
        <FileCard
          v-for="(item, index) in commDeciDetail?.files"
          :key="index"
          :name="item.name"
          :url="getFullAssetsUrl(item.url)"
        />
      </div>
    </div>
  </div>
</template>
