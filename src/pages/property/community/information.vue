<script>
import { mapState } from 'vuex'
import { getFullAssetsUrl } from '@/utils'
import { getCommunityInfoApi } from '@/services/api/property/community'
import Navbar from '@/components/Navbar.vue'

export default {
  components: { Navbar },
  data() {
    return {
      currentSwipeIndex: 0,
      communityInfo: {},
    }
  },
  computed: {
    ...mapState('user', ['propertyUserInfo']),

    // 隐藏业委会信息
    hiddenCommittee() {
      return this.propertyUserInfo?.sunshine.residents?.hidden_ywh
    },

    // 隐藏物业信息
    hiddenProperty() {
      return this.propertyUserInfo?.sunshine.residents?.hidden_wy
    },

    // 隐藏小区综合党委
    hiddenParty() {
      return this.propertyUserInfo?.sunshine.residents?.hidden_zhdw
    },

    list() {
      return [
        {
          icon: getFullAssetsUrl('/wxapp/property/community_committee.png'),
          text: '业委会信息',
          bgColor: '#EBF5FF',
          path: '/property/community/committee',
          hidden: this.hiddenCommittee,
        },
        {
          icon: getFullAssetsUrl('/wxapp/property/community_real.png'),
          text: '物业信息',
          bgColor: '#FFF2E5',
          path: '/property/community/property',
          hidden: this.hiddenProperty,
        },
        {
          icon: getFullAssetsUrl('/wxapp/property/community_party.png'),
          text: '小区综合党委',
          bgColor: '#EBF0FF',
          path: '/property/community/party',
          hidden: this.hiddenParty,
        },
      ]
    },

    newList() {
      return this.list.filter((item) => !item.hidden)
    },
  },
  beforeMount() {
    this.fetchCommunityInfoData()
  },
  methods: {
    async fetchCommunityInfoData() {
      const res = await getCommunityInfoApi()
      this.communityInfo = res
    },
  },
}
</script>

<template>
  <div class="p-3.5">
    <Navbar title="小区信息" />

    <van-swipe
      :autoplay="5000"
      class="community-swiper overflow-hidden rounded-lg"
      @change="(index) => (currentSwipeIndex = index)"
    >
      <van-swipe-item v-for="item in communityInfo.images" :key="item">
        <img :src="item" class="size-full" />
      </van-swipe-item>
      <template #indicator>
        <div
          v-if="communityInfo.images"
          class="font-350 absolute bottom-8 right-2 h-5 rounded-full bg-black/40 px-2 text-xs leading-5 text-white"
        >
          共{{ currentSwipeIndex + 1 }}/{{ communityInfo.images.length }}
        </div>
      </template>
    </van-swipe>

    <div class="z-9 relative -top-6 overflow-hidden rounded-lg bg-white px-3 py-4">
      <div
        class="center absolute right-0 top-3 rounded-l-full px-2 py-1.5 text-white"
        style="background: linear-gradient(90deg, #3377ff 4%, #3399ff 101%)"
      >
        小区规约
        <van-icon name="arrow" />
      </div>
      <div class="mb-0.5 text-lg font-bold">
        {{ communityInfo.community_name }}
      </div>
      <div class="mb-3.5 text-xs text-[#666]">
        {{ communityInfo.full_name }}
      </div>
      <div class="flex flex-col gap-y-3">
        <p class="row">
          <span>简介</span>
          <span>{{ communityInfo.introduction }}</span>
        </p>
        <p class="row">
          <span>地址</span>
          <span>{{ communityInfo.address }}</span>
        </p>
        <p class="row">
          <span>电话</span>
          <span>{{ communityInfo.contact_tel }}</span>
        </p>
      </div>

      <div :class="`grid-cols-${newList.length}`" class="my-4 grid gap-x-2">
        <div
          v-for="(item, index) in newList"
          :key="index"
          :style="{ backgroundColor: item.bgColor }"
          class="flex flex-col items-center overflow-hidden rounded-lg py-2"
          @click="$router.push(item.path)"
        >
          <img :src="item.icon" class="size-6" />
          <span>{{ item.text }}</span>
        </div>
      </div>
      <!--小区信息-->
      <div class="mb-5">
        <div class="mb-3.5 text-base font-bold">小区信息</div>
        <div class="grid grid-cols-2 gap-4">
          <p class="row">
            <span>建筑面积:</span>
            <span>{{ communityInfo.area }}万方</span>
          </p>
          <p class="row">
            <span>电梯数量:</span>
            <span>{{ communityInfo.elevator_num }}台</span>
          </p>
          <p class="row">
            <span>小区户数:</span>
            <span>{{ communityInfo.house_num }}户</span>
          </p>
          <p class="row">
            <span>绿化率:</span>
            <span>{{ communityInfo.greening_rate }}%</span>
          </p>
          <p class="row">
            <span>楼栋数:</span>
            <span>{{ communityInfo.buildings_num }}栋</span>
          </p>
          <p class="row">
            <span>单元数:</span>
            <span>{{ communityInfo.unit_num }}个</span>
          </p>
          <p class="row">
            <span>交付时间:</span>
            <span>{{ communityInfo.delivery_time }}</span>
          </p>
          <p class="row">
            <span>物业费:</span>
            <span>{{ communityInfo.fees }}</span>
          </p>
        </div>
      </div>

      <!--公共收益资源-->
      <div>
        <div class="mb-3.5 text-base font-bold">公共收益资源</div>
        <div class="grid grid-cols-2 gap-y-4">
          <p class="row">
            <span>车位收费:</span>
            <span>{{ communityInfo.parking_fee }}元/月</span>
          </p>
          <p class="row">
            <span>租赁摊位:</span>
            <span>{{ communityInfo.booth_fees }}</span>
          </p>
          <p class="row">
            <span>广告收费:</span>
            <span>{{ communityInfo.ad_fee }}元/月</span>
          </p>
          <p class="row">
            <span>活动场所费:</span>
            <span>{{ communityInfo.square_fee }}元/人</span>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.community-swiper {
  height: 210px;
}

.row {
  @apply flex items-start gap-x-2 text-sm;

  & > span:first-of-type {
    @apply shrink-0 text-[#999];
  }

  & > span:last-of-type {
    @apply flex-1 text-justify;
  }
}
</style>
