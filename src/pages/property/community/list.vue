<script>
import { mapActions } from 'vuex'
import { getStreetCommunityListApi } from '@/services/api/property/community'
import Navbar from '@/components/Navbar.vue'
import Iconfont from '@/components/Iconfont.vue'

export default {
  components: { Iconfont, Navbar },
  data() {
    return {
      currentCity: '',
      communities: [],
    }
  },
  computed: {
    indexList() {
      return this.communities.map((item) => item.title)
    },
  },
  beforeMount() {
    this.init()
  },
  methods: {
    ...mapActions('user', ['setCommunityAction']),
    async init() {
      const res = await getStreetCommunityListApi()
      this.communities = res
    },
    // 切换小区
    async handleCommunityItemClick(id) {
      await this.setCommunityAction(id)
      // 跳转到阳光物业首页
      this.$router.push({ name: 'property' })
    },
  },
}
</script>

<template>
  <div>
    <Navbar title="选择小区"> </Navbar>
    <div class="flex items-center gap-x-2 bg-white px-3.5 py-5">
      <Iconfont name="weizhi" />
      当前城市：{{ currentCity || '定位中...' }}
    </div>

    <van-index-bar :index-list="indexList">
      <div v-for="item in communities" :key="item.id">
        <van-index-anchor :index="item.title">
          {{ item.title }}
        </van-index-anchor>
        <van-cell
          v-for="community in item.communities"
          :key="community.id"
          :title="community.community_name"
          is-link
          @click="handleCommunityItemClick(community.id)"
        />
      </div>
    </van-index-bar>
  </div>
</template>

<style lang="scss" scoped>
:deep(.van-index-bar__sidebar) {
  display: flex;
  flex-direction: column;
  row-gap: 16px;

  .van-index-bar__index {
    font-size: 14px;

    &--active {
      color: #3377ff;
    }
  }
}

:deep(.van-index-anchor--sticky) {
  color: #3377ff;
}
</style>
