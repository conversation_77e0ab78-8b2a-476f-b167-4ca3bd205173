<script>
import { getFullAssetsUrl, isEmpty } from '@/utils'
import Navbar from '@/components/Navbar.vue'
import Phone from '@/components/Phone.vue'

export default {
  components: { Phone, Navbar },
  data() {
    return {
      memberInfo: null,
    }
  },
  beforeMount() {
    this.memberInfo = this.$route.query
  },
  methods: {
    isEmpty,
    getFullAssetsUrl,
  },
}
</script>

<template>
  <div class="p-3.5">
    <Navbar title="物业工作人员"></Navbar>
    <!-- 个人信息卡片 -->
    <div class="card">
      <div class="flex gap-x-2">
        <img :src="getFullAssetsUrl(memberInfo.avatar_url)" alt="" class="size-12 rounded-full" />
        <div class="flex flex-1 justify-between">
          <div class="flex flex-col justify-between">
            <div class="flex items-center gap-x-2">
              <span class="text-lg font-bold">
                {{ memberInfo.realname }}
              </span>
              <span class="rounded bg-blue-500 px-2 text-white">
                {{ memberInfo.position }}
              </span>
            </div>
            <span class="text-sm text-gray-500">
              {{ memberInfo.phone }}
            </span>
          </div>
          <Phone :phone-number="memberInfo.phone" />
        </div>
      </div>
      <div v-if="!isEmpty(memberInfo?.attributes)" class="mt-4 flex items-center text-base">
        <span class="text-[#999]"> 保安证编号： </span>
        <span>{{ memberInfo?.attributes?.baoan_number }}</span>
      </div>
    </div>
  </div>
</template>
