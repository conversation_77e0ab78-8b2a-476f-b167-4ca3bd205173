<script>
import { formatDate, formatMoney, getFullAssetsUrl } from '@/utils'
import { getFinanceBillCateListApi, getFinanceChartDataApi } from '@/services/api/property/finance'
import navbarBgColor from '@/mixins/navbarBgColor'
import Navbar from '@/components/Navbar.vue'
import Iconfont from '@/components/Iconfont.vue'
import VueEChart from '@/components/Chart/VueEChart.vue'
import Calendar from '@/components/Calendar.vue'
import DateSwitcher from '@/components/DateSwitcher.vue'

export default {
  components: { Navbar, Iconfont, VueEChart: VueEChart, Calendar, DateSwitcher },
  mixins: [navbarBgColor('#37f')],
  data() {
    return {
      accountId: this.$route.query.accountId,
      financeType: 'calendar',
      dateSwitcherType: 'month',
      financeTimeSearch: new Date(),
      currentTimeScope: 'day',
      financeChartData: [],
      chartLoading: false,
      calendarData: {},
      tabsData: [
        { text: '收入分类', value: 1 },
        { text: '支出分类', value: 2 },
      ],
      currentTabIndex: 0,
      openDateTimePicker: false,
      dateTimeValue: new Date(),
      billAccountInfo: null,
      billCateList: [],
      billStatMonth: null,
      timeScopeTabs: [
        { label: '日', value: 'day' },
        { label: '月', value: 'month' },
        { label: '季', value: 'quarter' },
        { label: '年', value: 'year' },
      ],
    }
  },
  computed: {
    swiperBillAccountInfo() {
      if (!this.billAccountInfo) return []
      const { account_no, bank_name, account_name } = this.billAccountInfo
      return [account_no, bank_name, account_name]
    },
    filterIncome() {
      return this.tabsData[this.currentTabIndex].value
    },
    calendarUnit() {
      return this.currentTimeScope === 'day' ? 'k' : 'w'
    },

    yAxisMax() {
      return Math.max(...this.financeChartData.map((item) => item.value))
    },

    yAxisMin() {
      return Math.min(...this.financeChartData.map((item) => item.value))
    },

    max() {
      return Math.max(Math.abs(this.yAxisMax), Math.abs(this.yAxisMin)) || 0
    },
    opts() {
      return {
        grid: {
          left: 5,
          top: 15,
          right: 10,
          bottom: 15,
          containLabel: true,
        },
        legend: { show: false },
        xAxis: {
          type: 'category',
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: true,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          data: this.financeChartData.map((item) => item.label),
        },
        yAxis: {
          type: 'value',
          name: '（万）',
          nameTextStyle: {
            align: 'right',
            color: '#666666',
            padding: [0, 0, 0, 15],
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#D6D6D6',
            },
          },
          axisLabel: {
            color: '#666666',
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
            },
          },
          startValue: 0,
          minInterval: 1,
          min: -Math.ceil(this.max) - 8,
          max: Math.ceil(this.max) + 8,
          splitNumber: 5,
        },
        series: {
          type: 'bar',
          barWidth: 8,
          itemStyle: {
            borderRadius: 8,
          },

          data: this.financeChartData.map((item) => ({
            value: item.value,
            itemStyle: {
              color: item.value > 0 ? '#F15556' : '#00B384',
            },
            label: {
              show: true,
              value: item.value > 0 ? `+${item.value}` : `-${item.value}`,
              color: item.value > 0 ? '#F15556' : '#00B384',
              position: item.value > 0 ? 'top' : 'bottom',
            },
          })),
        },
        dataZoom: [
          {
            type: 'inside',
            zoomLock: true,
            startValue: 0,
            endValue: 6,
          },
          {
            type: 'slider',
            zoomLock: true,
            show: false,
          },
        ],
      }
    },
  },
  mounted() {
    this.fetchBankBillCateList()
    this.fetchFinanceChartData()
  },
  methods: {
    getFullAssetsUrl,
    formatMoney,
    formatDate,

    async fetchBankBillCateList() {
      const { list, accountInfo, statMonth } = await getFinanceBillCateListApi({
        month: formatDate(this.dateTimeValue, 'YYYYMM'),
        filter_income: this.filterIncome,
        account_id: this.accountId,
      })
      this.billAccountInfo = accountInfo
      this.billCateList = list
      this.billStatMonth = statMonth
    },

    async fetchFinanceChartData() {
      this.chartLoading = true
      const map = {
        day: formatDate(this.financeTimeSearch, 'YYYY-MM'),
        month: formatDate(this.financeTimeSearch, 'YYYY'),
        quarter: formatDate(this.financeTimeSearch, 'YYYY'),
        year: '',
      }
      const { charts } = await getFinanceChartDataApi({
        time_scope: this.currentTimeScope,
        time_search: map[this.currentTimeScope],
      })
      const newCharts = charts.map((item) => ({
        value: Number(item.income) - Number(item.expend),
        label: item.label,
      }))
      this.financeChartData = newCharts.map((item) => ({
        ...item,
        value: (item.value / 10000).toFixed(2),
      }))
      this.chartLoading = false
      this.getCalendarData(newCharts)
    },

    getCalendarData(calendar) {
      const processors = {
        day: () => {
          const date = formatDate(this.financeTimeSearch, 'YYYY-MM')
          return calendar.map((item) => [
            `${date}-${item.label.replace(/[\u4E00-\u9FA5]/g, '')}`,
            item.value,
          ])
        },
        month: () => {
          const date = formatDate(this.financeTimeSearch, 'YYYY')
          return calendar.map((item) => [
            `${date}-${item.label.replace(/[\u4E00-\u9FA5]/g, '')}`,
            item.value,
          ])
        },
        quarter: () => {
          const names = ['第一季度', '第二季度', '第三季度', '第四季度']
          return calendar.map((item, index) => [names[index], item.value])
        },
        year: () => {
          return calendar.map((item) => [item.label.replace(/[\u4E00-\u9FA5]/g, ''), item.value])
        },
      }

      const processor = processors[this.currentTimeScope]
      if (!processor) return
      this.calendarData = Object.fromEntries(processor())
    },

    handleStatClick(type) {
      const params = {
        month: formatDate(this.dateTimeValue, 'YYYYMM'),
        filterIncome: type,
        accountId: this.accountId,
      }
      this.$router.push({ path: '/property/finance/bank/water', query: params })
    },

    handleCateClick(item) {
      const params = {
        month: formatDate(this.dateTimeValue, 'YYYYMM'),
        cateId: item.cate_id,
        cateName: item.cate.cate_name,
        filterIncome: this.filterIncome,
        accountId: this.accountId,
      }

      this.$router.push({ path: '/property/finance/bank/list', query: params })
    },

    handleTabChange() {
      this.fetchBankBillCateList()
    },

    handleTimeScopeChange(value) {
      if (this.currentTimeScope === value) return
      this.dateSwitcherType = value === 'day' ? 'month' : 'year'
      this.currentTimeScope = value
      this.fetchFinanceChartData()
    },

    handleDateSwitcherChange() {
      this.fetchFinanceChartData()
    },

    handleCalendarDateSwitch(date) {
      this.financeTimeSearch = date
      this.fetchFinanceChartData()
    },
  },
}
</script>

<template>
  <div class="wrapper flex min-h-full flex-col">
    <Navbar :id="NAVBAR_ID" :bg-color="navBarBackgroundColor" color="#fff" title="小区财务明细">
    </Navbar>
    <div :id="PAGE_CONTAINER_ID" class="mt-2.5 flex flex-1 flex-col px-3.5">
      <div class="card mb-2.5">
        <div class="flex justify-between">
          <div class="flex-1 overflow-hidden">
            <!-- 账户信息 -->
            <div class="pb-2.5">
              <div class="text-placeholder mb-2 flex items-center gap-x-2 text-sm">账户信息</div>
              <van-swipe :show-indicators="false">
                <van-swipe-item v-for="(item, index) in swiperBillAccountInfo" :key="index">
                  {{ item }}
                </van-swipe-item>
              </van-swipe>
            </div>

            <div>
              <div class="flex items-center gap-x-1 text-base">
                <img :src="getFullAssetsUrl('/wxapp/property/finance.png')" class="size-5" />
                <span style="margin-top: 1px"> 账户余额 </span>
              </div>
              <div class="mt-1 text-xl text-[#3377FF]">
                {{ formatMoney(billAccountInfo?.balance) }}
              </div>
            </div>
          </div>

          <div class="flex flex-col justify-between">
            <!-- 账号图片 -->
            <img
              :src="getFullAssetsUrl(billAccountInfo?.bank_icon)"
              style="width: 80px; height: 68px"
            />

            <van-button
              class="rounded-md"
              color="#37f"
              size="small"
              @click="$router.push('/property/finance/bank/chart')"
            >
              <Iconfont name="shujuzhexiantu" size="16px" />
              收支分析
            </van-button>
          </div>
        </div>

        <div class="pt-3">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-x-4">
              <div
                :class="{ 'bg-[#EAF1FF]': financeType === 'calendar' }"
                class="flex h-7 w-9 items-center justify-center rounded"
                @click="financeType = 'calendar'"
              >
                <img
                  v-if="financeType === 'chart'"
                  :src="getFullAssetsUrl('/wxapp/property/calendar.png')"
                  class="size-4"
                />
                <img
                  v-else
                  :src="getFullAssetsUrl('/wxapp/property/calendar_active.png')"
                  class="size-4"
                />
              </div>
              <div
                :class="{ 'bg-[#EAF1FF]': financeType === 'chart' }"
                class="flex h-7 w-9 items-center justify-center rounded"
                @click="financeType = 'chart'"
              >
                <img
                  v-if="financeType === 'calendar'"
                  :src="getFullAssetsUrl('/wxapp/property/chart_bar.png')"
                  class="size-4"
                />
                <img
                  v-else
                  :src="getFullAssetsUrl('/wxapp/property/chart_bar_active.png')"
                  class="size-4"
                />
              </div>
            </div>

            <div class="flex items-center gap-x-1.5">
              <div
                v-for="item in timeScopeTabs"
                :key="item.value"
                :class="{ 'bg-[#EAF1FF] text-blue-500': currentTimeScope === item.value }"
                class="rounded px-2 py-1"
                @click="handleTimeScopeChange(item.value)"
              >
                {{ item.label }}
              </div>
            </div>
          </div>
          <div class="pt-4">
            <template v-if="financeType === 'chart'">
              <DateSwitcher
                v-if="currentTimeScope !== 'year'"
                v-model="financeTimeSearch"
                :type="dateSwitcherType"
                @change="handleDateSwitcherChange"
              />

              <VueEChart :loading="chartLoading" :option="opts" height="285px" />
            </template>
            <template v-else>
              <div class="-mx-4">
                <Calendar
                  :list="calendarData"
                  :mode="currentTimeScope"
                  :unit="calendarUnit"
                  custom-class="p-0!"
                  @date-switch="handleCalendarDateSwitch"
                />
              </div>
            </template>
          </div>
        </div>
      </div>

      <div class="card mb-2.5">
        <div class="flex flex-wrap items-center justify-between">
          <div class="font-500 text-xl" @click="openDateTimePicker = true">
            {{ formatDate(dateTimeValue, 'YYYY年MM') }}
            <van-icon name="arrow-down" />
          </div>
          <div
            class="rounded-2xl border border-solid border-blue-500 bg-blue-500/10 px-2 py-1 text-blue-500"
            @click="handleStatClick(0)"
          >
            全部流水
            <van-icon name="arrow" />
          </div>
        </div>

        <div class="mt-4 flex gap-x-2.5">
          <div
            class="relative flex flex-1 flex-col items-center gap-y-0.5 overflow-hidden rounded-xl bg-[#F15556] py-2 text-white"
            @click="handleStatClick(1)"
          >
            <img
              :src="getFullAssetsUrl('/wxapp/property/right-circle-simple-fill.svg')"
              class="absolute right-2 top-1/2 size-5 -translate-y-1/2"
            />
            <div>{{ formatDate(dateTimeValue, 'M月') }}收入（元）</div>
            <div class="font-500 text-lg">
              {{ billStatMonth?.income_amount }}
            </div>
            <div>共{{ billStatMonth?.income_count }}笔</div>
          </div>
          <div
            class="relative flex flex-1 flex-col items-center gap-y-0.5 overflow-hidden rounded-xl bg-[#5A7BFF] py-2 text-white"
            @click="handleStatClick(2)"
          >
            <img
              :src="getFullAssetsUrl('/wxapp/property/right-circle-simple-fill.svg')"
              class="absolute right-2 top-1/2 size-5 -translate-y-1/2"
            />
            <div>{{ formatDate(dateTimeValue, 'M月') }}支出（元）</div>
            <div class="font-500 text-lg">
              {{ billStatMonth?.expend_amount }}
            </div>
            <div>共{{ billStatMonth?.expend_count }}笔</div>
          </div>
        </div>

        <div class="mt-4">
          <van-tabs v-model="currentTabIndex" color="#37f" @change="handleTabChange">
            <van-tab v-for="(item, index) in tabsData" :key="index" :title="item.text">
              <div class="mt-4">
                <div
                  v-for="item in billCateList"
                  :key="item.cate_id"
                  class="flex items-center gap-x-2 border-b border-neutral-100 px-1 py-3"
                  @click="handleCateClick(item)"
                >
                  <div
                    v-if="Number(item.cate.income) === 1"
                    class="flex size-9 items-center justify-center rounded-lg bg-[#FF4922]/10 text-base font-medium text-[#FF4922]"
                  >
                    收
                  </div>
                  <div
                    v-else
                    class="flex size-9 items-center justify-center rounded-lg bg-[#5A7BFF]/10 text-base font-medium text-[#5A7BFF]"
                  >
                    支
                  </div>
                  <div class="flex-1">
                    <div class="line-clamp-1">
                      {{ item.cate.cate_name }}
                    </div>
                    <div class="text-xs text-[#999]">{{ item.icount }}笔</div>
                  </div>
                  <div class="ml-a text-base font-medium">
                    {{ Number(item.cate.income) === 1 ? '+' : '-' }}{{ item.amount }}
                  </div>
                  <div>
                    <van-icon name="arrow" />
                  </div>
                </div>
              </div>
            </van-tab>
          </van-tabs>
        </div>
      </div>
    </div>

    <van-popup v-model="openDateTimePicker" position="bottom">
      <van-datetime-picker
        v-model="dateTimeValue"
        title="选择年月"
        type="year-month"
        @confirm="
          () => {
            openDateTimePicker = false
            fetchBankBillCateList()
          }
        "
      />
    </van-popup>
  </div>
</template>

<style lang="scss" scoped>
.wrapper {
  background-repeat: no-repeat;
  background-size: 100% 178px;
  background-image: linear-gradient(
    180deg,
    #3377ff 37%,
    rgba(51, 119, 255, 0.81) 69%,
    rgba(51, 119, 255, 0) 91%
  );
}

.button-wrapper {
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);
}

:deep(.tn-tabs) {
  margin: 0 -32rpx;
}
</style>
