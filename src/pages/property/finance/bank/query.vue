<script>
import { getFinanceDetailApi, getQuestionListApi } from '@/services/api/property/finance'
import {
  extractFileNameAndExtension,
  formatDate,
  formatMoney,
  getFullAssetsUrl,
  isEmpty,
} from '@/utils'
import { IsSelfEnum, QuestionStatusEnum } from '@/enums/property'
import createListRefresh from '@/mixins/listRefresh'
import Navbar from '@/components/Navbar.vue'

export default {
  components: { Navbar },
  mixins: [
    createListRefresh(getQuestionListApi, function () {
      return {
        topic: this.query.topic,
        data_id: this.query.id,
        self: this.currentTabIndex,
      }
    }),
  ],
  data() {
    return {
      query: this.$route.query,
      financeInfo: {},
      paymentInfo: {},
      payeeInfo: {},
      questionList: [],
      currentTabIndex: IsSelfEnum.YES,
      tabsData: [
        { text: '我的质疑', value: IsSelfEnum.YES },
        { text: '其他质疑', value: IsSelfEnum.NO },
      ],
    }
  },
  created() {
    this.init()
  },
  methods: {
    formatDate,
    formatMoney,
    getFullAssetsUrl,
    extractFileNameAndExtension,
    isEmpty,

    async init() {
      this.fetchFinanceDetail()
      this.fetchQuestionList()
    },

    //  获取财务详情数据
    async fetchFinanceDetail() {
      const { accountInfo, info } = await getFinanceDetailApi({
        id: this.query.id,
        account_id: this.query.accountId,
      })
      this.financeInfo = info
      this.paymentInfo = {
        name: info.trade_sign === '-' ? accountInfo.account_name : info.replay_company,
        account: info.trade_sign === '-' ? accountInfo.account_no : info.replay_account,
      }
      this.payeeInfo = {
        name: info.trade_sign === '-' ? info.replay_company : accountInfo.account_name,
        account: info.trade_sign === '-' ? info.replay_account : accountInfo.account_no,
        bankName: info.trade_sign === '-' ? info.replay_bank : accountInfo.bank_name,
      }
    },

    // 获取质疑列表
    async fetchQuestionList() {
      const data = await getQuestionListApi({
        topic: this.query.topic,
        data_id: this.query.id,
        self: this.currentTabIndex,
      })
      console.log('🚀 ~ fetchQuestionList ~ data: ', data)
      this.questionList = data
    },
    handleTabsChange(index) {
      this.currentTabIndex = this.tabsData[index].value
      this.fetchQuestionList()
    },
    openFile(file) {
      window.open(file, '_blank')
    },
    handleButtonClick() {
      // 模拟跳转
      alert('跳转到提出质疑页')
    },
    goDetail(id) {
      alert('跳转到详情页：' + id)
    },

    getQuestionTagColor(status) {
      switch (status) {
        case QuestionStatusEnum.PendingVerification:
          return { color: 'rgb(255,121,46)', bgColor: 'rgba(255,121,46,0.1)' }
        case QuestionStatusEnum.Processing:
          return { color: 'rgb(64,158,255)', bgColor: 'rgba(64,158,255,0.1)' }
        case QuestionStatusEnum.Completed:
          return { color: 'rgb(0,179,132)', bgColor: 'rgba(0,179,132,0.1)' }
      }
    },

    getStatusText(status) {
      const statusMap = {
        [QuestionStatusEnum.Processing]: '处理中',
        [QuestionStatusEnum.PendingVerification]: '待核验',
        [QuestionStatusEnum.Completed]: '已完结',
      }
      return statusMap[status] || ''
    },
  },
}
</script>

<template>
  <div class="p-3.5">
    <Navbar title="质疑留言"></Navbar>

    <div class="z-1 card rounded-b-0 relative">
      <div class="flex flex-col items-center gap-y-2 border-b border-neutral-100 pb-4">
        <img
          :src="
            getFullAssetsUrl(
              financeInfo.cate?.icon ||
                (financeInfo.trade_sign === '-'
                  ? '/wxapp/property/bill_cate_expend.png'
                  : '/wxapp/property/bill_cate_income.png')
            )
          "
          alt=""
          class="size-9"
        />
        <div class="mt-2">{{ financeInfo.trade_remark }}</div>
        <div
          :style="{ color: financeInfo.trade_sign === '-' ? '#333' : '#FF4922' }"
          class="text-xl"
        >
          {{ (financeInfo.trade_sign || '+') + formatMoney(financeInfo.trade_amount) }}
        </div>
      </div>

      <div class="row-group px-10 pt-4">
        <div class="row">
          <span>交易时间:</span>
          <span>{{ formatDate(financeInfo.create_time) }}</span>
        </div>
        <div class="row">
          <span>流水号:</span>
          <span>{{ financeInfo.trade_no }}</span>
        </div>
        <div class="row">
          <span>付款对象:</span>
          <span>{{ paymentInfo.name || '--' }}</span>
        </div>
        <div class="row">
          <span>付方账号:</span>
          <span>{{ paymentInfo.account || '--' }}</span>
        </div>
        <div class="row">
          <span>收方名称:</span>
          <span>{{ payeeInfo.name || '--' }}</span>
        </div>
        <div class="row">
          <span>收方开户行:</span>
          <span>{{ payeeInfo.bankName || '--' }}</span>
        </div>
        <div class="row">
          <span>收方账号:</span>
          <span>{{ payeeInfo.account }}</span>
        </div>
        <div class="row">
          <span>交易备注:</span>
          <span>{{ financeInfo.trade_remark }}</span>
        </div>
        <div v-if="!isEmpty(financeInfo.images)" class="row">
          <span>图片:</span>
          <img :src="getFullAssetsUrl(financeInfo.images)" class="image" />
        </div>

        <div v-if="!isEmpty(financeInfo.attaches)" class="row">
          <span>附件:</span>
          <div v-for="(file, i) in financeInfo.attaches" :key="i" class="attachment">
            <img
              :src="
                getFullAssetsUrl(`/wxapp/common/${extractFileNameAndExtension(file).extension}.png`)
              "
              alt=""
              class="size-7"
            />
            <a href="javascript:void(0);" @click="openFile(getFullAssetsUrl(file))">
              {{ extractFileNameAndExtension(file).fileName }}.{{
                extractFileNameAndExtension(file).extension
              }}
            </a>
          </div>
        </div>
      </div>
    </div>

    <div class="card mt-2.5">
      <van-tabs color="#37f">
        <van-tab v-for="(item, index) in tabsData" :key="index" :title="item.text">
          <div class="question-list">
            <div
              v-for="item in questionList"
              :key="item.id"
              class="question-item"
              @click="goDetail(item.id)"
            >
              <img :src="getFullAssetsUrl(item.profile.avatar_url)" class="avatar" />
              <div>
                <div>{{ item.profile.realname }}</div>
                <div>{{ formatDate(item.create_time) }}</div>
                <div>{{ item.content }}</div>
                <span
                  :style="{
                    backgroundColor: getQuestionTagColor(item.status).bgColor,
                    color: getQuestionTagColor(item.status).color,
                    borderColor: getQuestionTagColor(item.status).color,
                  }"
                  class="tag"
                >
                  {{ getStatusText(item.status) }}
                </span>
              </div>
            </div>
          </div>
        </van-tab>
      </van-tabs>

      <div class="footer">
        <button @click="handleButtonClick">提出质疑</button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.row {
  & > span:first-of-type {
    width: 78px;
  }

  & > span:last-of-type {
    text-align: left;
  }
}
</style>
