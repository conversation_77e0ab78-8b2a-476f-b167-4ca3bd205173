<script>
import { formatDate } from '@/utils'
import { getBankBillDataApi, getFinanceCateApi } from '@/services/api/property/finance'
import createListRefresh from '@/mixins/listRefresh'
import Navbar from '@/components/Navbar.vue'
import Iconfont from '@/components/Iconfont.vue'
import SelectTags from '@/components/SelectTags.vue'

export default {
  components: { Navbar, Iconfont, SelectTags },
  mixins: [
    createListRefresh(getBankBillDataApi, {
      getQuery() {
        return {
          month: formatDate(this.dateTimeValue, 'YYYYMM'),
          cate_id: this.selectCateId[0],
          filter_income: this.filterIncome[0],
          account_id: this.$route.query.accountId,
          trade_remark: this.searchValue,
        }
      },
      dataHandler(res, vm) {
        vm.billAccountInfo = res.accountInfo
        vm.billStatInfo = res.statInfo
        return {
          data: res.list.data,
          total: res.list.total,
        }
      },
    }),
  ],
  data() {
    return {
      searchValue: '',
      openDateTimePicker: false,
      dateTimeValue: new Date(),
      showPopup: false,
      currentSelectTabIndex: 0,
      filterIncome: [Number(this.$route.query.filterIncome)],
      selectCateId: [],
      incomeCateList: [],
      expendCateList: [],
      billAccountInfo: null,
      billStatInfo: null,
      billList: [],
    }
  },
  computed: {
    isIncome() {
      return Number(this.$route.query.filterIncome) === 1
    },
    isExpense() {
      return Number(this.$route.query.filterIncome) === 2
    },
    isAll() {
      return Number(this.$route.query.filterIncome) === 0
    },
    title() {
      if (this.isIncome) return '收入流水'
      if (this.isExpense) return '支出流水'
      return '全部流水'
    },
    selectTabsData() {
      if (this.isExpense) return ['支出类目']
      if (this.isIncome) return ['收入类目']
      return ['支出类目', '收入类目']
    },
    showIncome() {
      return (item) => (this.isAll || this.isIncome) && item.split_info?.count_income
    },
    showExpense() {
      return (item) => (this.isAll || this.isExpense) && item.split_info?.count_expend
    },
    getTagItems() {
      return (cateList) => {
        if (!cateList) return []
        return cateList.map((item) => ({
          label: item.cate_name,
          value: item.id,
        }))
      }
    },
  },
  created() {
    this.fetchBankBillCateData()
  },
  methods: {
    formatDate,

    async fetchBankBillCateData() {
      const { income, expend } = await getFinanceCateApi(this.$route.query.accountId)
      this.incomeCateList = income
      this.expendCateList = expend
    },

    handleCateClick(id) {
      if (!id) {
        this.selectCateId = []
      } else {
        this.selectCateId = [+id]
      }
      this.onRefresh()
    },

    handleResetFilter() {
      this.filterIncome = []
      this.selectCateId = []
    },

    handleConfirmFilter() {
      this.onRefresh()
      this.showPopup = false
    },

    handleItemClick(id) {
      this.$router.push({
        path: '/property/finance/bank/query',
        query: { id, topic: 'bill' },
      })
    },
  },
}
</script>

<template>
  <div>
    <Navbar :title="title"></Navbar>
    <div>
      <div class="bg-white p-3.5">
        <div class="rounded-full bg-neutral-100">
          <van-search
            v-model="searchValue"
            placeholder="搜索账单备注"
            shape="round"
            @search="onRefresh"
          />
        </div>
      </div>
      <div class="flex items-center p-3.5">
        <div class="flex-1 overflow-hidden">
          <div class="hide-scrollbar flex w-full flex-nowrap items-center gap-x-2 overflow-x-auto">
            <div
              :class="[!selectCateId.length && '!bg-blue-500/10 !text-blue-500']"
              class="text-nowrap rounded-2xl bg-white px-3 py-1"
              @click="handleCateClick('')"
            >
              全部
            </div>
            <div v-for="item in billStatInfo?.tabs" :key="item.id">
              <span
                :class="[selectCateId.includes(+item.id) && '!bg-blue-500/10 !text-blue-500']"
                class="text-nowrap rounded-2xl bg-white px-3 py-1"
                @click="handleCateClick(item.id)"
              >
                {{ item.cate_name }}
              </span>
            </div>
          </div>
        </div>
        <div class="pl-2 text-base" @click="showPopup = true">
          筛选
          <Iconfont name="shaixuan" size="16px" />
        </div>
      </div>
    </div>
    <van-list v-model="loading" :finished="finished" @load="fetchListData">
      <div class="card mx-3.5 flex flex-col">
        <div>
          <div class="text-base" @click="openDateTimePicker = true">
            {{ formatDate(dateTimeValue, 'YYYY年MM') }}
            <van-icon name="arrow-down" />
          </div>
          <div class="mt-2 flex gap-x-4">
            <div v-if="isIncome || isAll">收入：¥{{ billStatInfo?.income }}</div>
            <div v-if="isExpense || isAll">支出：¥{{ billStatInfo?.expend }}</div>
          </div>
        </div>

        <div v-for="item in list" :key="item.id">
          <div v-if="item.show_split" class="text-placeholder pt-3">
            <div>{{ item.split_info.day }}</div>
            <div class="flex flex-wrap items-center gap-x-1.5">
              <div v-if="showIncome(item)" class="flex items-center gap-x-1">
                <span>收入{{ item.split_info?.count_income }}笔</span>
                :
                <span>¥{{ item.split_info?.sum_income_amount }}</span>
              </div>
              <div v-if="showExpense(item)" class="flex items-center gap-x-1">
                <span>支出{{ item.split_info?.count_expend }}笔</span>
                :
                <span>¥{{ item.split_info?.sum_expend_amount }}</span>
              </div>
            </div>
          </div>
          <div
            class="flex items-center gap-x-3 border-b border-neutral-100 px-1 py-3"
            @click="handleItemClick(item.id)"
          >
            <div
              v-if="!item.trade_sign"
              class="font-500 flex size-9 items-center justify-center rounded-lg bg-[#FF4922]/10 text-base text-[#FF4922]"
            >
              收
            </div>
            <div
              v-else
              class="font-500 flex size-9 items-center justify-center rounded-lg bg-[#5A7BFF]/10 text-base text-[#5A7BFF]"
            >
              支
            </div>
            <div class="flex-1">
              <div class="line-clamp-1">{{ item.cate?.cate_name || item.trade_remark }}</div>
              <div class="text-nowrap text-[#999] text-[20rpx]">
                {{ formatDate(item.create_time, 'YYYY/MM/DD HH:mm:ss') }}
              </div>
            </div>
            <div class="ml-a">
              <div :class="item.trade_sign || 'text-red-500'" class="text-right text-base">
                {{ (item.trade_sign || '+') + item.trade_amount }}
              </div>
              <div class="flex items-center justify-end gap-x-1 text-xs">
                <div class="rounded-2xl bg-[#37F]/10 px-2 py-[2px] text-[#37f]">
                  质疑：{{ item.doubts_count }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-list>

    <van-popup v-model="openDateTimePicker" position="bottom">
      <van-datetime-picker
        v-model="dateTimeValue"
        title="选择年月"
        type="year-month"
        @confirm="
          () => {
            openDateTimePicker = false
            onRefresh()
          }
        "
      />
    </van-popup>

    <van-popup v-model="showPopup" position="bottom">
      <div class="flex flex-col">
        <van-tabs v-model="currentSelectTabIndex" color="#37f">
          <van-tab v-for="(item, index) in selectTabsData" :key="index" :title="item">
            <div class="mt-3 h-[420px] flex-1 overflow-y-auto px-3.5">
              <template v-if="(isExpense || isAll) && currentSelectTabIndex === 0">
                <SelectTags
                  v-model="filterIncome"
                  :items="[{ label: '全部支出', value: 2 }]"
                  cancelable
                  @click="selectCateId = []"
                />
                <div class="col gap-y-2 py-4">
                  <div v-for="item in expendCateList" :key="item.id">
                    <div class="text-placeholder-color mb-2">
                      {{ item.cate_name }}
                    </div>
                    <SelectTags
                      v-model="selectCateId"
                      :items="getTagItems(item.children)"
                      cancelable
                      @click="filterIncome = []"
                    />
                  </div>
                </div>
              </template>
              <template v-if="(isIncome || isAll) && currentSelectTabIndex === 1">
                <SelectTags
                  v-model="filterIncome"
                  :items="[{ label: '全部收入', value: 1 }]"
                  cancelable
                  @click="selectCateId = []"
                />
                <div class="col gap-y-2 py-4">
                  <div>
                    <div class="text-placeholder-color mb-2">收入分类</div>
                    <SelectTags
                      v-model="selectCateId"
                      :items="getTagItems(incomeCateList)"
                      cancelable
                      @click="filterIncome = []"
                    />
                  </div>
                </div>
              </template>
            </div>
          </van-tab>
        </van-tabs>

        <div class="button-wrapper flex items-center justify-between bg-white p-4">
          <van-button
            class="h-10 w-[120px] !bg-blue-500/10 !text-blue-500"
            @click="handleResetFilter"
          >
            重置
          </van-button>
          <van-button class="h-10 w-[210px]" color="#37f" @click="handleConfirmFilter">
            确认
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<style lang="scss" scoped>
.button-wrapper {
  box-shadow: 0 0 10rpx 0 rgba(0, 0, 0, 0.1);
}

.hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome/Safari/Webkit */
}
</style>
