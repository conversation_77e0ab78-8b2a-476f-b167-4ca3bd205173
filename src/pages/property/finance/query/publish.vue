<script>
import { formatMoney } from '@/utils'
import { publishQuestionApi } from '@/services/api/property/finance'
import Navbar from '@/components/Navbar.vue'
import FixedBottom from '@/components/FixedBottom.vue'

export default {
  components: { FixedBottom, Navbar },
  data() {
    return {
      query: this.$route.query,
      money: '',
      yearQuarterTxt: '',
      cate: '',
      topic: this.query.topic,
      data_id: this.query.data_id,
      questionFormRef: null,
      questionParams: {
        topic: this.$route.query.topic,
        data_id: this.$route.query.data_id,
        content: '',
        images: [],
      },
      questionRules: {
        content: [{ required: true, message: '请输入质疑内容', trigger: 'blur' }],
      },
    }
  },
  mounted() {
    const eventChannel = this.getOpenerEventChannel && this.getOpenerEventChannel()
    if (eventChannel) {
      eventChannel.on('getYearQuarterTxt', (data) => {
        this.money = data.money
        this.yearQuarterTxt = data.yearQuarterTxt
        this.cate = data.cate
      })
    }
  },
  methods: {
    formatMoney,

    async submitQuestion() {
      try {
        await this.$refs.questionFormRef.validate()
        await publishQuestionApi(this.questionParams)
        this.$router.back()
      } catch (err) {
        console.error(err)
      }
    },
  },
}
</script>

<template>
  <div class="p-3.5">
    <Navbar title="提出质疑"></Navbar>

    <div class="card">
      <div class="row">
        <span>质疑类目</span>
        <span class="text-secondary">
          <template v-if="topic === 'bill'">
            {{ cate }}
          </template>
          <template v-else>
            {{ yearQuarterTxt }}{{ topic === 'benefit:expend' ? '本期支出' : '本期收入' }}
          </template>
        </span>
      </div>
      <div class="row">
        <span>质疑金额</span>
        <span class="text-secondary">{{ formatMoney(money) }}元</span>
      </div>
      <div class="mt-3">
        <van-form
          ref="questionFormRef"
          :model="questionParams"
          :rules="questionRules"
          label-position="top"
          size="lg"
        >
          <van-field
              v-model="questionParams.content"
              name="用户名"
              label="用户名"
              placeholder="用户名"
              :rules="[{ required: true, message: '请填写用户名' }]"
          />
          <tn-form-item label="上传图片">
            <image-upload v-model="questionParams.images" />
          </tn-form-item>
        </-form>
      </div>
    </div>
    <FixedBottom>
      <van-button block @click="submitQuestion">提交</van-button>
    </FixedBottom>
  </div>
</template>

<style lang="scss" scoped>
.row {
  @apply flex justify-between items-center text-base py-4 border-b border-neutral-100;
}
</style>
