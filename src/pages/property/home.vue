<script>
import { mapGetters, mapState } from 'vuex'
import { formatDate, formatMoney, getFullAssetsUrl } from '@/utils'
import { getHouseOwnershipsApi } from '@/services/api/property/user'
import { getFinanceAccountApi } from '@/services/api/property/finance'
import { getNoticeListApi, getResolutionListApi } from '@/services/api/property/bulletin'
import { communityServerList } from './resource'
import createListRefresh from '@/mixins/listRefresh'
import Iconfont from '@/components/Iconfont.vue'
import navbarBgColor from '@/mixins/navbarBgColor'
import Navbar from '@/components/Navbar.vue'

export default {
  components: {
    Iconfont,
    Navbar,
  },
  mixins: [
    navbarBgColor('#37f'),
    createListRefresh(getNoticeListApi, {
      prefix: 'notice',
    }),
    createListRefresh(getResolutionListApi, {
      prefix: 'resolution',
    }),
  ],
  data() {
    return {
      selectFinanceTabIndex: 0,
      publicAccount: null, // 公共收益账户
      companyAccount: null, // 物业公司账户
      ownerList: [], // 房产信息
      communityServerList, // 小区服务
    }
  },
  computed: {
    ...mapState('user', ['communityId']),
    ...mapGetters('user', ['isVerified', 'communityName']),

    currentMonth() {
      return this.formatDate(undefined, 'M')
    },
  },
  beforeMount() {
    this.init()
  },
  methods: {
    getFullAssetsUrl,
    formatMoney,
    formatDate,

    init() {
      this.fetchFinanceData()
      this.fetchHouseOwnershipData()
    },

    // 未绑定房屋弹框提醒
    unBindHouseDialog() {
      this.$dialog
        .confirm({
          title: '温馨提示',
          message: '您还不是当前小区的居民，请绑定房屋后再操作',
          confirmButtonText: '去绑定',
          confirmButtonColor: '#37f',
        })
        .then(() => {
          this.$router.push({
            path: '/pages-property/pages/house/bind',
          })
        })
        .catch(() => {})
    },

    // 获取财务信息
    async fetchFinanceData() {
      const { account_ggsy, account_wygk } = await getFinanceAccountApi()
      this.publicAccount = account_ggsy
      this.companyAccount = account_wygk
    },

    // 获取不动产权的房产信息
    async fetchHouseOwnershipData() {
      const res = await getHouseOwnershipsApi()
      this.ownerList = res
    },

    // 财务卡片，公共收益账户和物业公司账户切换
    handleFinanceAccountSwitch(index) {
      this.selectFinanceTabIndex = index
    },

    // 点击财务卡片
    /**
     * 处理财务卡片点击事件
     * 根据小区ID和选择的标签页获取对应的账户ID，然后跳转到银行流水页面
     */
    handleFinanceCardClick() {
      // 验证用户是否已认证
      if (!this.isVerified) {
        return this.unBindHouseDialog()
      }

      // 获取账户ID
      const accountId = this.getAccountId()
      if (!accountId) {
        return this.$toast.fail('暂未配置')
      }

      // 跳转到银行流水页面
      this.navigateToFinancePage(accountId)
    },

    /**
     * 获取当前选择的账户ID
     * @returns {string|null} 账户ID或null
     */
    getAccountId() {
      const { communityId, selectFinanceTabIndex } = this
      const isSpecialCommunity = +communityId === 11

      // 特殊小区（ID为11）只使用公司账户
      if (isSpecialCommunity) {
        return this.companyAccount?.account_id || null
      }

      // 普通小区根据选择的标签页返回对应账户
      const isPublicTab = selectFinanceTabIndex === 0
      const targetAccount = isPublicTab ? this.publicAccount : this.companyAccount

      return targetAccount?.account_id || null
    },

    /**
     * 跳转到财务银行流水页面
     * @param {string} accountId 账户ID
     */
    navigateToFinancePage(accountId) {
      this.$router.push({
        path: '/property/finance/bank',
        query: { accountId }
      })
    },

    // 页面跳转
    handlePageJump(path) {
      // if (!this.isVerified) {
      //   return this.unBindHouseDialog()
      // }
      this.$router.push({
        path,
      })
    },

    // 通知公告点击
    handleNoticeClick(id) {
      this.$router.push({
        path: `/property/bulletin/notice/${id}`,
      })
    },

    // 公示公告点击
    handleDecisionClick(id) {
      this.$router.push({
        path: `/property/bulletin/publicity/${id}`,
      })
    },
  },
}
</script>

<template>
  <div
    :style="{ backgroundImage: `url(${getFullAssetsUrl('/wxapp/property/home_bg.png')})` }"
    class="size-full overflow-y-auto bg-white bg-[length:100%_256px] bg-no-repeat p-3.5"
  >
    <Navbar :id="NAVBAR_ID" :bg-color="navBarBackgroundColor" :is-back="false">
      <template #left>
        <div
          class="flex items-center gap-x-2 text-white"
          @click="$router.push('/property/community/list')"
        >
          <span class="text-base">{{ communityName }}</span>
          <Iconfont name="xiala" />
        </div>
      </template>
    </Navbar>

    <!--    财务数据卡片-->
    <div :id="PAGE_CONTAINER_ID" class="mb-2.5">
      <div
        class="relative z-20 flex h-[35px] rounded-t-2xl bg-[#DDEAFF] text-sm leading-[35px] text-[#022E8A]"
      >
        <div class="relative flex-1 pl-10" @click="handleFinanceAccountSwitch(0)">
          <div
            v-if="selectFinanceTabIndex === 0"
            :style="{
              backgroundImage: `url(${getFullAssetsUrl(
                '/wxapp/property/finance_switch_left.png'
              )})`,
            }"
            class="absolute bottom-0 left-0 z-10 h-11 w-[202px] bg-cover bg-left-top bg-no-repeat"
          />
          <span :class="selectFinanceTabIndex === 0 && 'text-white'" class="relative z-10">
            公共收益账户
          </span>
        </div>

        <div class="relative flex-1 pr-10 text-right" @click="handleFinanceAccountSwitch(1)">
          <div
            v-if="selectFinanceTabIndex === 1"
            :style="{
              backgroundImage: `url(${getFullAssetsUrl(
                '/wxapp/property/finance_switch_right.png'
              )})`,
            }"
            class="absolute bottom-0 right-0 z-10 h-11 w-[202px] bg-cover bg-left-top bg-no-repeat"
          />
          <span :class="selectFinanceTabIndex === 1 && 'text-white'" class="relative z-10">
            物业公司账户
          </span>
        </div>
      </div>

      <!--        南都苑小区 -->
      <!--      <div-->
      <!--        v-else-->
      <!--        class="flex items-center justify-between gap-x-4 rounded-t-4 from-[#4073EB] to-[#A4BDFE] bg-gradient-to-b px-3.5 pb-7.5 pt-2 span-white"-->
      <!--      >-->
      <!--        <div class="line-clamp-1 flex-1 span-base font-bold" @tap="handleFinanceClick">-->
      <!--          {{ companyAccount?.account_name }}-->
      <!--        </div>-->
      <!--      </div>-->

      <div class="relative z-10 h-[157px] overflow-hidden rounded-b-2xl">
        <!--        公共收益账户-->
        <template v-if="selectFinanceTabIndex === 0">
          <div
            :style="{
              backgroundImage: `url(${getFullAssetsUrl('/wxapp/property/home_finance.png')})`,
            }"
            class="h-full overflow-hidden rounded-b-xl bg-cover bg-left-top bg-no-repeat px-6 pt-4"
            @click="handleFinanceCardClick"
          >
            <div class="flex items-center justify-between text-[#666]">
              <span> 公共收益账户余额(元) </span>
              <span v-if="publicAccount" class="text-xs">
                数据截止: {{ formatDate(publicAccount.last_update) }}
              </span>
            </div>
            <div v-if="publicAccount" class="mt-1.5 text-xs text-[#999]">
              账号：{{ isVerified ? publicAccount?.account_no : '***' }}
            </div>
            <div v-else class="mt-1.5 text-xs text-red-500">当前账户暂未公开</div>

            <div
              class="flex items-end gap-x-1 font-[Poppins] text-blue-500"
              style="font-size: 26px"
            >
              <span>¥</span>
              <span v-if="!publicAccount" class="-mb-1">--</span>
              <span v-else-if="isVerified">
                {{ formatMoney(publicAccount?.balance) }}
              </span>
              <span v-else class="-mb-1 text-2xl">***</span>
            </div>
            <div class="flex">
              <div class="flex-1">
                <div class="flex items-center gap-x-1 text-[#666]">
                  <img
                    :src="getFullAssetsUrl('/wxapp/property/income_icon.png')"
                    class="mb-[2px] size-3.5"
                  />
                  <span>{{ currentMonth }}月份收入</span>
                </div>
                <div class="font-[Poppins] text-xl text-[#191E3A]">
                  <span v-if="!publicAccount">--</span>
                  <span v-else-if="isVerified">
                    {{ formatMoney(publicAccount?.month_income) }}
                  </span>
                  <span v-else>***</span>
                </div>
              </div>
              <div class="flex-1">
                <div class="flex items-center gap-x-1 text-[#666]">
                  <img
                    :src="getFullAssetsUrl('/wxapp/property/expense_icon.png')"
                    alt=""
                    class="mb-[2px] size-3.5"
                  />
                  <span>{{ currentMonth }}月份支出</span>
                </div>
                <div class="font-[Poppins] text-xl text-[#191E3A]">
                  <span v-if="!publicAccount">--</span>
                  <span v-else-if="isVerified">
                    {{ formatMoney(publicAccount?.month_expend) }}
                  </span>
                  <span v-else>***</span>
                </div>
              </div>
            </div>
          </div>
        </template>
        <!--        物业公司账户-->
        <template v-else>
          <div
            :style="{
              backgroundImage: `url(${getFullAssetsUrl('/wxapp/property/home_finance.png')})`,
            }"
            class="h-full overflow-hidden rounded-b-xl bg-cover bg-left-top bg-no-repeat px-6 pt-4"
            @click="handleFinanceCardClick"
          >
            <div class="flex items-center justify-between">
              <span> 物业公司账户余额(元) </span>
              <span v-if="companyAccount" class="text-xs text-[#666]">
                数据截止: {{ formatDate(companyAccount.last_update, 'MM/DD HH:mm') }}
              </span>
            </div>
            <div v-if="companyAccount" class="mt-1.5 text-xs text-[#999]">
              账号：{{ isVerified ? companyAccount?.account_no : '***' }}
            </div>
            <div v-else class="mt-1.5 text-xs text-red-500">当前账户暂未公开</div>
            <div
              class="flex items-end gap-x-1 font-[Poppins] text-blue-500"
              style="font-size: 26px"
            >
              <span>¥</span>
              <span v-if="!companyAccount" class="-mb-1">--</span>
              <span v-else-if="isVerified">
                {{ formatMoney(companyAccount?.balance) }}
              </span>
              <span v-else class="-mb-1 text-2xl">***</span>
            </div>
            <div class="flex">
              <div class="flex-1">
                <div class="flex items-center gap-x-1 text-[#666]">
                  <img
                    :src="getFullAssetsUrl('/wxapp/property/income_icon.png')"
                    alt=""
                    class="mb-[2px] size-3.5"
                  />
                  <span>{{ currentMonth }}月份收入</span>
                </div>
                <div class="font-[Poppins] text-xl text-[#191E3A]">
                  <span v-if="!companyAccount">--</span>
                  <span v-else-if="isVerified">
                    {{ formatMoney(companyAccount?.month_income) }}
                  </span>
                  <span v-else>***</span>
                </div>
              </div>
              <div class="flex-1">
                <div class="flex items-center gap-x-1 text-[#666]">
                  <img
                    :src="getFullAssetsUrl('/wxapp/property/expense_icon.png')"
                    class="mb-[2px] size-3.5"
                  />
                  <span>{{ currentMonth }}月份支出</span>
                </div>
                <div class="font-[Poppins] text-xl text-[#191E3A]">
                  <span v-if="!companyAccount">--</span>
                  <span v-else-if="isVerified">
                    {{ formatMoney(companyAccount?.month_expend) }}
                  </span>
                  <span v-else>***</span>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!--    房产信息、缴费情况、物业排行榜-->
    <div class="mb-2.5 grid grid-cols-2 gap-x-1 gap-y-2">
      <div
        :style="{
          backgroundImage: `url(${getFullAssetsUrl('/wxapp/property/业主信息_bg.png')})`,
        }"
        class="relative row-span-2 bg-cover bg-left-top bg-no-repeat pl-3.5 pt-2"
      >
        <van-swipe
          v-if="ownerList.length"
          :autoplay="true"
          :circular="true"
          :interval="5000"
          class="h-full"
        >
          <van-swipe-item v-for="(item, index) in ownerList" :key="index">
            <div class="mb-2 flex items-center gap-x-2">
              <span class="text-lg font-medium">
                {{ item.realname }}
              </span>
              <img :src="getFullAssetsUrl('/wxapp/property/业主1_icon.png')" class="w-15 h-6" />
            </div>
            <div class="mb-2 flex items-center gap-x-1">
              <p class="text-sm">
                <span> {{ item?.building_no }}幢 </span>
                <span v-if="item.unit_no !== '0'"> {{ item?.unit_no }}单元 </span>
              </p>
              <span class="text-sm text-[#999]"> ｜ </span>
              <span class="text-sm"> {{ item?.house_no }}室 </span>
            </div>
            <div class="text-sm">{{ item?.area }}m²</div>
          </van-swipe-item>
        </van-swipe>
        <p v-else class="pl-5 pt-6 text-lg font-medium text-[#001C46]">不动产权认证</p>
        <van-button
          class="absolute bottom-6 right-2 h-6 w-[80px] px-0 !text-xs text-white"
          color="linear-gradient(260deg, #4c7efb 6%, #57d7ff 98%)"
          icon="arrow"
          icon-position="right"
          round
          text="我的房屋"
          @click="handlePageJump('/property/house/owner')"
        >
        </van-button>
      </div>
      <!--      缴费情况-->
      <div
        :style="{
          backgroundImage: `url(${getFullAssetsUrl('/wxapp/property/缴费情况_bg.png')})`,
        }"
        class="flex h-20 items-center bg-cover bg-left-top bg-no-repeat text-lg font-medium text-[#1B2E86]"
        @click="handlePageJump('/property/payment/case')"
      >
        <div class="ml-auto mr-6">缴费情况<van-icon name="arrow" /></div>
      </div>
      <!--      物业排行榜-->
      <div
        :style="{
          backgroundImage: `url(${getFullAssetsUrl('/wxapp/property/物业排行榜_bg.png')})`,
        }"
        class="flex h-20 items-center bg-cover bg-left-top bg-no-repeat text-lg font-medium text-[#9A5516]"
        @click="handlePageJump('/property/ranking')"
      >
        <div class="ml-auto mr-6">物业排行榜<van-icon name="arrow" /></div>
      </div>
    </div>

    <!--    小区服务-->
    <div class="server-card overflow-hidden rounded-xl bg-white">
      <div
        :style="{
          backgroundImage: `url(${getFullAssetsUrl('/wxapp/property/home_cate_head.png')})`,
        }"
        class="flex h-[46px] items-center bg-[#F1F8FE] bg-cover bg-left-top bg-no-repeat px-3 text-lg font-medium"
      >
        小区服务
      </div>
      <div class="grid grid-cols-4 gap-y-4 py-4">
        <div
          v-for="(item, index) in communityServerList"
          :key="index"
          class="flex flex-col items-center gap-y-2"
          @click="handlePageJump(item.path)"
        >
          <img :alt="item.name" :src="item.icon" class="size-10" />
          <span class="text-xs font-[350]">{{ item.name }}</span>
        </div>
      </div>
    </div>

    <!--    通知公告/公示公告-->
    <div class="mt-2.5">
      <van-tabs color="#37f">
        <van-tab title="通知公告">
          <van-list v-model="noticeLoading" :finished="noticeFinished" @load="noticeFetchListData">
            <div class="flex flex-col gap-y-1.5">
              <div v-for="item in noticeList" :key="item.id" @click="handleNoticeClick(item.id)">
                <div class="flex justify-between gap-x-2 border-b border-neutral-100 bg-white p-3">
                  <div class="flex flex-1 flex-col justify-between">
                    <div class="mb-2.5 line-clamp-2 text-base">{{ item.title }}</div>
                    <div class="flex items-center gap-x-2 text-xs text-[#A8A5A4]">
                      <span>{{ item.create_time }}</span>
                      <span>{{ communityName }}</span>
                    </div>
                  </div>
                  <img
                    v-if="item.images.length"
                    :src="getFullAssetsUrl(item.images[0])"
                    alt=""
                    class="h-20 w-28 rounded object-cover"
                  />
                </div>

                <div class="flex justify-between px-4 py-2">
                  <div class="flex items-center gap-x-1">
                    <Iconfont name="yanjing1" />
                    <span>{{ item.view_num }}</span>
                  </div>
                  <div class="flex items-center gap-x-1">
                    <Iconfont name="pinglun" />
                    <span>{{ item.disc_num }}</span>
                  </div>
                  <div class="flex items-center gap-x-1">
                    <Iconfont name="iconfontzhizuobiaozhun023148" />
                    <span>{{ item.like_num }}</span>
                  </div>
                </div>
              </div>
            </div>
          </van-list>
        </van-tab>
        <van-tab title="公示公告">
          <van-list
            v-model="resolutionLoading"
            :finished="resolutionFinished"
            @load="resolutionFetchListData"
          >
            <div class="flex flex-col gap-y-1.5 px-3.5">
              <div
                v-for="item in resolutionList"
                :key="item.id"
                class="border-b border-neutral-100 py-3"
                @click="handleDecisionClick(item.id)"
              >
                <div class="line-clamp-2 text-base">{{ item.title }}</div>
                <div class="mt-2.5 flex items-center justify-between text-xs text-[#999]">
                  <span>{{ item.create_time }}</span>
                  <div class="flex items-center gap-x-1">
                    <Iconfont name="yanjing1"></Iconfont>
                    <span>{{ item.view_num }}</span>
                  </div>
                </div>
              </div>
            </div>
          </van-list>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.van-button__span) {
  font-size: 12px;
}

:deep(.van-icon) {
  margin-left: 0;
  font-size: 14px;
}

/* 小区服务 */
.server-card {
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 14%);
}
</style>
