<script>
import { mapActions } from 'vuex'
import { getFullAssetsUrl } from '@/utils'
import { getHouseOwnershipsApi } from '@/services/api/property/house'
import Navbar from '@/components/Navbar.vue'
import Iconfont from '@/components/Iconfont.vue'

export default {
  components: {
    Navbar,
    Iconfont,
  },

  data() {
    return {
      list: [],
      modalVisible: false,
      modalConfig: {
        title: '',
        content: '',
        showCancel: false,
        confirm: null,
      },
    }
  },

  beforeMount() {
    this.fetchListData()
  },

  methods: {
    getFullAssetsUrl,

    ...mapActions('user', ['setCommunityAction']),

    async fetchListData() {
      const res = await getHouseOwnershipsApi()
      console.log('🚀 ~ fetchListData ~ res: ', res)
      this.list = res
    },

    // 切换小区
    async handleSelectCom(id, enter) {
      if (!enter) {
        return this.$toast({
          title: '当前小区还未入驻平台',
          icon: 'error',
        })
      }
      try {
        await this.setCommunityAction(id)
        await this.fetchMyHouseList()
      } catch (error) {
        console.error('切换小区失败:', error)
      }
    },

    // 格式化房屋编号
    formatHouseNumber(index) {
      return String(index + 1).padStart(2, '0')
    },

    // 模态框确认
    handleModalConfirm() {
      if (this.modalConfig.confirm) {
        this.modalConfig.confirm()
      }
      this.modalVisible = false
    },

    // 模态框取消
    handleModalCancel() {
      this.modalVisible = false
    },
  },
}
</script>
<template>
  <div>
    <Navbar title="不动产房屋"></Navbar>

    <!-- 房屋列表 -->
    <div v-if="list.length" class="flex flex-col gap-y-2 p-3">
      <div
        v-for="(item, index) in list"
        :key="item.id"
        class="relative overflow-hidden rounded-lg bg-white p-3.5 shadow-md"
      >
        <!-- 房屋编号标签 -->
        <div class="-mx-3.5 -mt-3.5 mb-3.5 flex justify-between">
          <div
            class="h-7.5 flex w-fit items-center justify-center rounded-r-full bg-blue-500/10 px-4 text-sm text-blue-500"
          >
            房屋{{ formatHouseNumber(index) }}
          </div>
        </div>

        <!-- 房屋信息主体 -->
        <div class="relative">
          <!-- 小区信息和切换按钮 -->
          <div class="mb-2 flex items-center gap-1">
            <div class="max-w-[45%] text-base font-bold text-gray-800">
              {{ item.community_name || '暂无小区信息' }}
            </div>

            <div
              v-if="item.selected"
              class="rounded-md bg-gray-200 px-1.5 py-1 text-xs text-gray-400"
            >
              当前小区
            </div>

            <div
              v-else
              :class="
                  item.enter === false
                    ? 'bg-gray-400 text-white cursor-not-allowed'
                    : 'bg-blue-500 text-white hover:bg-blue-600'
                "
              class="flex cursor-pointer items-center gap-1 rounded-md px-1.5 py-1 text-sm"
              @click="handleSelectCom(item.community_id, item.enter)"
            >
              <Iconfont name="qiehuan" />
              <span>切换小区</span>
            </div>
          </div>

          <!-- 房屋状态图标 -->
          <div class="absolute -top-8 right-0">
            <img
              v-if="item.enter"
              :src="
                  getFullAssetsUrl(
                    '/storage/default/20250612/组 <EMAIL>'
                  )
                "
              class="h-17 w-21"
            />
            <img
              v-else
              :src="
                  getFullAssetsUrl(
                    '/storage/default/20250612/组 6967@3x (1)728a35b73e05b0ef0b9abebe21c1f62ce137c77c.png'
                  )
                "
              class="h-17 w-21"
            />
          </div>

          <!-- 业主信息 -->
          <div class="mt-2 flex items-center gap-1.5">
            <div class="text-lg text-gray-800">
              {{ item.realname }}
            </div>
            <img :src="getFullAssetsUrl('wxapp/property/业主1_icon.png')" class="w-15 h-6" />
          </div>

          <!-- 房屋详细信息 -->
          <div class="mt-2 flex gap-2.5 text-sm text-gray-600">
              <span>
                {{ item?.building_no }}幢
                <span v-if="item.unit_no !== '0'"> {{ item?.unit_no }}单元 </span>
              </span>
            <span class="text-gray-300">｜</span>
            <span>{{ item?.house_no }}室</span>
            <span class="text-gray-300">｜</span>
            <span>{{ item?.area }}m²</span>
          </div>

          <!-- 房屋名称 -->
          <div class="my-2 font-light text-gray-800">
            {{ item.house_name }}
          </div>

          <!-- 其他业主信息 -->
          <div class="mr-3 flex flex-wrap items-center gap-1.5 border-t border-gray-100 pt-3.5">
            <template v-for="(user, userIndex) in item.owners">
              <div :key="`name-${userIndex}`" class="text-lg text-gray-800">
                {{ user.realname }}
              </div>
              <img
                :key="`badge-${userIndex}`"
                :src="getFullAssetsUrl('wxapp/property/业主1_icon.png')"
                alt=""
                class="w-15 h-6"
              />
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <van-empty v-else description="描述文字" />
  </div>
</template>

<style scoped></style>
