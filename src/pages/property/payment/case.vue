<script>
import { mapGetters, mapState } from 'vuex'
import { getPaymentInfoApi } from '@/services/api/property/payment'
import { getFullAssetsUrl, getYearRange } from '@/utils'
import Navbar from '@/components/Navbar.vue'
import Iconfont from '@/components/Iconfont.vue'
import VueEChart from '@/components/Chart/VueEChart.vue'

export default {
  components: { VueEChart: VueEChart, Navbar, Iconfont },
  data() {
    return {
      openYearPicker: false,
      currentYear: new Date().getFullYear().toString() + '年',
      yearColumns: getYearRange().map((year) => `${year}年`),

      openRangePicker: false,
      currentRangePickerValue: '收费概括',
      rangePickerData: ['收费概括', '入驻用户比例'],

      openPicker: false,
      pickerIndex: 0,

      chartLoading: false,
      chartData: null,
      paymentOptions: [],
    }
  },
  computed: {
    ...mapState('user', ['propertyUserInfo']),
    ...mapGetters('user', ['communityName']),

    yearPickerDefaultIndex() {
      return this.yearColumns.findIndex((year) => year === this.currentYear)
    },
    rangePickerDefaultIndex() {
      return this.rangePickerData.findIndex((item) => item === this.currentRangePickerValue)
    },
    pickerData() {
      return this.paymentOptions.map((item) => item.dong_name)
    },
    hidden_hoa() {
      return this.propertyUserInfo?.sunshine.residents?.hidden_hoa
    },
    houseData() {
      return this.paymentOptions[this.pickerIndex] || {}
    },

    // 收费概览
    paymentChartData() {
      return [
        {
          name: '已缴户数',
          value: this.chartData?.valid ?? 0,
          itemStyle: {
            color: '#D93E0D',
          },
        },
        {
          name: '未缴户数',
          value: this.chartData?.invalid ?? 0,
          itemStyle: {
            color: '#D9D9D9',
          },
        },
      ]
    },
    // 入驻用户比例
    enterChartData() {
      return [
        {
          name: '已入驻',
          value: this.chartData?.verified ?? 0,
          itemStyle: {
            color: '#F1C836',
          },
        },
        {
          name: '未入驻',
          value: (this.chartData?.total ?? 0) - (this.chartData?.verified ?? 0),
          itemStyle: {
            color: '#9C86F5',
          },
        },
      ]
    },

    option() {
      return {
        tooltip: {
          trigger: 'item',
        },
        legend: {
          top: 'center',
          right: '20',
          orient: 'vertical',
          icon: 'circle',
          itemWidth: 6,
          itemHeight: 6,
          itemGap: 24,
          textStyle: {
            fontSize: 14,
          },
          formatter: (name) => {
            const item =
              this.currentRangePickerValue === '收费概括'
                ? this.paymentChartData
                : this.enterChartData
            const value = item.find((item) => item.name === name)?.value
            return `${name}   ${value}   ${((value / (this.chartData?.total ?? 1)) * 100).toFixed(
              2
            )}%`
          },
        },
        series: [
          {
            name: this.currentRangePickerValue,
            type: 'pie',
            radius: ['53%', '80%'],
            center: ['19%', '50%'],
            avoidLabelOverlap: false,
            padAngle: 5,
            label: {
              show: false,
              position: 'center',
            },
            labelLine: {
              show: false,
            },
            data:
              this.currentRangePickerValue === '收费概括'
                ? this.paymentChartData
                : this.enterChartData,
          },
        ],
      }
    },
  },
  beforeMount() {
    this.fetchPaymentInfoData()
  },
  methods: {
    getFullAssetsUrl,

    async fetchPaymentInfoData() {
      this.chartLoading = true
      const { subtotal, options } = await getPaymentInfoApi(this.currentYear.replace(/年/g, ''))
      this.chartData = subtotal
      this.paymentOptions = options
      this.$nextTick(() => {
        this.chartLoading = false
      })
    },

    handleYearPickerConfirm(val) {
      this.currentYear = val
      this.fetchPaymentInfoData()
      this.openYearPicker = false
    },

    handleYearPickerCancel() {
      this.openYearPicker = false
    },

    handleRangePickerConfirm(val) {
      this.currentRangePickerValue = val
      this.openRangePicker = false
    },

    handleRangePickerCancel() {
      this.openRangePicker = false
    },

    handleHousePickerConfirm(val) {
      this.pickerIndex = this.pickerData.indexOf(val)
      this.openPicker = false
    },

    handleHousePickerCancel() {
      this.openPicker = false
    },
  },
}
</script>

<template>
  <div>
    <Navbar title="缴费情况"></Navbar>

    <div class="flex flex-col gap-y-2.5 p-3.5">
      <div class="card">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="text-base text-black">
              {{ currentRangePickerValue }}
            </div>
            <div class="ml-4 flex items-center gap-x-1" @click="openYearPicker = true">
              <span>{{ currentYear }}</span>
              <van-icon class="mb-1" name="arrow-down" />
            </div>
          </div>
          <div
            class="-mr-3 flex items-center justify-center gap-x-1 rounded-l-full px-2 py-0.5 text-white"
            style="background-image: linear-gradient(90deg, #3377ff 4%, #3399ff 101%)"
            @click="openRangePicker = true"
          >
            <span>切换</span>
            <Iconfont name="qiehuan"></Iconfont>
          </div>
        </div>
        <VueEChart :loading="chartLoading" :option="option" height="130px" />
      </div>

      <div class="card">
        <div
          class="-ml-3 mb-2 w-fit rounded-r-full px-3 py-2 text-sm text-white"
          style="background-image: linear-gradient(90deg, #3377ff 4%, #3399ff 101%)"
          @click="openPicker = true"
        >
          {{ communityName }}{{ pickerData[pickerIndex] }}
          <Iconfont name="qiehuan"></Iconfont>
        </div>

        <div v-if="!hidden_hoa">
          <div class="flex items-center">
            <div class="item">
              <span class="text-red text-2xl">{{ houseData.valid }}</span>
              <span>已缴</span>
            </div>
            <div class="line"></div>
            <div class="item">
              <span class="text-placeholder text-2xl">{{ houseData.invalid }}</span>
              <span>未缴</span>
            </div>
            <div class="line"></div>
            <div class="item">
              <span class="text-2xl text-[#F1C836]">{{ houseData.verified }}</span>
              <span>已入驻</span>
            </div>
          </div>
          <div class="flex justify-center gap-x-4">
            <div class="flex items-center gap-x-1">
              <div class="size-2 rounded-full bg-red-500"></div>
              <span>已缴</span>
            </div>
            <div class="flex items-center gap-x-1">
              <div class="size-2 rounded-full bg-[#999]"></div>
              <span>未缴</span>
            </div>
            <div class="flex items-center gap-x-1">
              <img :src="getFullAssetsUrl('/wxapp/common/verify.png')" alt="" class="size-4" />
              <span>已入驻</span>
            </div>
          </div>
        </div>

        <div v-else>
          <div class="flex items-center">
            <div class="item">
              <span class="text-2xl text-[#999]">{{ houseData.empty }}</span>
              <span>空置</span>
            </div>
            <div class="line"></div>
            <div class="item">
              <span class="text-2xl text-[#F1C836]">{{ houseData.verified }}</span>
              <span>已入驻</span>
            </div>
          </div>
          <div class="flex justify-center gap-x-3">
            <div class="flex items-center gap-x-1">
              <img :src="getFullAssetsUrl('/wxapp/common/party.png')" alt="" class="size-4" />
              <span>有党员</span>
            </div>
            <div class="flex items-center gap-x-1">
              <img :src="getFullAssetsUrl('/wxapp/common/verify.png')" alt="" class="size-4" />
              <span>已入驻</span>
            </div>
          </div>
        </div>

        <div class="mt-4">
          <div
            v-for="(values, key) in houseData.houses"
            :key="key"
            :class="`grid-cols-${values.length}`"
            class="mb-2 grid gap-2"
          >
            <div
              v-for="j in values"
              :key="j.id"
              :class="['relative flex h-[52px] items-center justify-center']"
              :style="{
                color: j.valid ? '#ff3D00' : '#666',
                backgroundColor: j.valid ? 'rgba(255, 61, 0, 0.1)' : 'rgba(102, 102, 102, 0.1)',
              }"
            >
              {{ j.full_name }}
              <div class="absolute left-0 top-0 flex items-center gap-x-1">
                <img
                  v-if="j.verified"
                  :src="getFullAssetsUrl('/wxapp/property/verify_icon.png')"
                  alt=""
                  class="size-4"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--    <div-->
    <!--      v-if="showEmptyDialog"-->
    <!--      :style="{ left: emptyLeft, top: emptyTop }"-->
    <!--      class="fixed z-50 w-44 rounded-md bg-black bg-opacity-70 p-2 text-center text-white"-->
    <!--    >-->
    <!--      <div class="rounded-br-2 rounded-tl-1 absolute left-0 top-0 bg-[#5E50FC] p-1 text-white">-->
    <!--        空置-->
    <!--      </div>-->
    <!--      <div class="mb-1">{{ empty.name }}</div>-->
    <!--      <div class="text-left text-xs">空置原因：{{ empty.content }}</div>-->
    <!--    </div>-->

    <van-popup v-model="openYearPicker" position="bottom">
      <van-picker
        :columns="yearColumns"
        :default-index="yearPickerDefaultIndex"
        show-toolbar
        title="选择年"
        @cancel="handleYearPickerCancel"
        @confirm="handleYearPickerConfirm"
      />
    </van-popup>

    <van-popup v-model="openRangePicker" position="bottom">
      <van-picker
        :columns="rangePickerData"
        :default-index="rangePickerDefaultIndex"
        show-toolbar
        @cancel="handleRangePickerCancel"
        @confirm="handleRangePickerConfirm"
      />
    </van-popup>

    <van-popup v-model="openPicker" position="bottom">
      <van-picker
        :columns="pickerData"
        show-toolbar
        @cancel="handleHousePickerCancel"
        @confirm="handleHousePickerConfirm"
      />
    </van-popup>
  </div>
</template>

<style scoped>
.item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding: 1rem 0;
}

.line {
  width: 1px;
  height: 20px;
  background: #cccccc;
}

.border-highlight {
  border-color: #5e50fc;
}
</style>
