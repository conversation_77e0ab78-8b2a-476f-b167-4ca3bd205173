import { getFullAssetsUrl } from '@/utils'

// 小区服务
export const communityServerList = [
  {
    icon: getFullAssetsUrl('/wxapp/property/home_xqxx.png'),
    name: '小区信息',
    path: '/property/community/information',
    permission: false,
  },
  {
    icon: getFullAssetsUrl('/wxapp/property/home_wyjf.png'),
    name: '我要缴费',
    path: '/pages-property/pages/payment/list',
    permission: true,
  },
  {
    icon: getFullAssetsUrl('/wxapp/property/home_zcfg.png'),
    name: '政策法规',
    path: '/property/bulletin/policy',
    permission: true,
  },
  {
    icon: getFullAssetsUrl('/wxapp/property/home_gzjl.png'),
    name: '质疑监督',
    path: '/pages-property/pages/finance/question/record/income-list',
    permission: true,
  },
  {
    icon: getFullAssetsUrl('/wxapp/property/home_ywh.png'),
    name: '我要建议',
    path: '/property/suggest',
    permission: true,
  },
  {
    icon: getFullAssetsUrl('/wxapp/property/session_icon.png'),
    name: '业主大会',
    path: `/pages-property/pages/web/session`,
    permission: true,
  },
  {
    icon: getFullAssetsUrl('/wxapp/property/active_icon.png'),
    name: '活动报名',
    path: `/pages-property/pages/more-module/activity/index`,
    permission: true,
  },
  {
    icon: getFullAssetsUrl('/wxapp/property/gdgn.png'),
    name: '更多功能',
    path: '/pages-property/pages/more-module/index',
    permission: true,
  },
]
