<script>
import { getFullAssetsUrl, hexOrRgbToRgba } from '@/utils'
import { DirectionEnum, IsHotEnum } from '@/enums/property'
import { getCommentListApi, publicCommentApi } from '@/services/api/property/comment'
import { doneSuggestApi, getSuggestDetailApi } from '@/services/api/property/suggest'
import createListRefresh from '@/mixins/listRefresh'
import Navbar from '@/components/Navbar.vue'
import Iconfont from '@/components/Iconfont.vue'
import Phone from '@/components/Phone.vue'
import FixedBottom from '@/components/FixedBottom.vue'
import SendMessage from '@/components/SendMessage.vue'

export default {
  components: { SendMessage, FixedBottom, Phone, Iconfont, Navbar },
  mixins: [
    createListRefresh(getCommentListApi, {
      getQuery() {
        return {
          data_id: this.$route.query.id,
          parent_id: 0,
          topic: 'suggest',
        }
      },
      prefix: 'comment',
    }),
  ],
  data() {
    return {
      DirectionEnum,
      IsHotEnum,
      suggestionDetail: null,
      replyPage: 1,
      replyTotal: 0,
      replyCommentId: 0,
      lastReplyCommentId: '',
      replyNickname: '',
      sendMessageFocus: false,
    }
  },
  computed: {
    sendMessagePlaceholder() {
      return this.replyCommentId ? `回复 ${this.replyNickname}：` : undefined
    },
  },
  beforeMount() {
    this.fetchSuggestionDetailData()
  },
  methods: {
    getFullAssetsUrl,
    hexOrRgbToRgba,

    async fetchSuggestionDetailData() {
      const id = this.$route.query.id
      this.suggestionDetail = await getSuggestDetailApi(id)
    },

    // 获取当前评论的回复评论
    async fetchReplyCommentList(parentId) {
      const { data, total } = await getCommentListApi({
        data_id: this.$route.query.id,
        parent_id: parentId,
        topic: 'suggest',
        page: this.replyPage,
      })

      const commentList = [...this.commentList]
      const index = commentList.findIndex((item) => item.id === parentId)
      if (index === -1) return
      commentList[index].children = data
      this.commentList = [...commentList]
      this.replyPage++
      this.replyTotal = total
      console.log('this.commentList', this.commentList)
    },

    // 发布评论
    async handlePublicComment(val) {
      await publicCommentApi({
        data_id: this.$route.query.id,
        parent_id: this.replyCommentId,
        topic: 'suggest',
        content: val,
        images: [],
      })
      this.commentRefreshListData()
    },

    // 回复评论
    handleReply(id, nickname) {
      this.replyCommentId = id
      this.replyNickname = nickname || '匿名用户'
      this.sendMessageFocus = true
    },

    // 展开剩余评论
    handleShowMoreComment(id) {
      if (this.lastReplyCommentId !== id) {
        this.replyPage = 1
      }
      this.lastReplyCommentId = id
      this.fetchReplyCommentList(id)
    },

    // 收起评论
    handleHideComment(id) {
      this.lastReplyCommentId = ''
      const commentList = [...this.commentList]
      const index = commentList.findIndex((item) => item.id === id)
      if (index === -1) return
      commentList[index].children = []
      this.commentList = [...commentList]
    },

    handleHotClick() {
      this.$dialog({
        title: '温馨提示',
        message: '当前话题参与点赞，评论人数已达到当前小区已注册业主的20%',
        confirmButtonText: '确定',
        confirmButtonColor: '#37f',
      }).then(() => {})
    },

    // 处理点赞/点踩操作
    async handleLike(id, direction) {
      this.updateLikeState(this.suggestionDetail, direction)
      await doneSuggestApi({ data_id: id, direction })
    },

    // 处理点赞/点踩的状态更新
    updateLikeState(item, direction) {
      const isUpVote = direction === DirectionEnum.UP
      const currentState = isUpVote ? 'up' : 'down'
      const oppositeState = isUpVote ? 'down' : 'up'

      // 切换当前操作的状态
      item[currentState] = !item[currentState]
      item[`${currentState}_num`] += item[currentState] ? 1 : -1

      // 如果存在相反的状态，则取消
      if (item[oppositeState]) {
        item[oppositeState] = false
        item[`${oppositeState}_num`] -= 1
      }
    },
  },
}
</script>

<template>
  <div class="p-3.5">
    <Navbar title="投票"> </Navbar>

    <div v-if="suggestionDetail" class="card">
      <div class="mb-5 flex items-center gap-x-2">
        <img
          :src="getFullAssetsUrl(suggestionDetail.user?.avatar_url)"
          alt=""
          class="size-10 rounded-full"
        />

        <div class="flex flex-col justify-between">
          <div>{{ suggestionDetail.user?.realname || '匿名用户' }}</div>
          <div class="text-[#666]">
            {{ suggestionDetail.user?.phone || '暂无' }}
          </div>
        </div>

        <div
          v-if="suggestionDetail.is_hot === IsHotEnum.YES"
          class="ml-auto flex items-center text-[#F56745]"
          @click.stop="handleHotClick"
        >
          <Iconfont name="remen" size="40rpx" />
          <span>热门</span>
          <van-icon class="ml-1" color="#888889" name="question-o" size="16px" />
        </div>

        <div v-if="suggestionDetail.user?.phone" class="ml-auto">
          <Phone :phone-number="suggestionDetail.user.phone" />
        </div>
      </div>
      <div class="row-group">
        <div>
          <div class="row">
            <span>意见</span>
            <span>{{ suggestionDetail.description }}</span>
          </div>
          <div class="mt-1 grid grid-cols-3 gap-x-2 pl-9">
            <img
              v-for="(image, i) in suggestionDetail.images"
              :key="i"
              :src="getFullAssetsUrl(image)"
              alt=""
              class="h-20 w-full rounded"
            />
          </div>
        </div>
        <div class="row">
          <span>时间</span>
          <span>{{ suggestionDetail.create_time }}</span>
        </div>
      </div>

      <div class="mt-2 flex items-center justify-center gap-x-8 text-[#A8A5A4]">
        <div class="flex items-center gap-x-1">
          <Iconfont name="yanjing1" />
          浏览量:
          <span>{{ suggestionDetail.view_num }}</span>
        </div>
        <div
          class="flex items-center gap-x-1"
          @click.stop="handleLike(suggestionDetail.id, DirectionEnum.UP)"
        >
          <Iconfont v-if="!suggestionDetail.up" custom-class="-mt-0.5" name="dianzan3" />
          <Iconfont v-else color="#F56745" custom-class="-mt-0.5" name="dianzan" />
          点赞:
          <span>{{ suggestionDetail.up_num }}</span>
        </div>
        <div
          class="flex items-center gap-x-1"
          @click.stop="handleLike(suggestionDetail.id, DirectionEnum.DOWN)"
        >
          <Iconfont v-if="!suggestionDetail.down" custom-class="mt-0.5" name="dianzan1" />
          <Iconfont v-else color="#F56745" custom-class="mt-0.5" name="dianzan2" />
          踩一下:
          <span>{{ suggestionDetail.down_num }}</span>
        </div>
      </div>
    </div>

    <div class="card mt-2.5">
      <div class="text-title">评论（{{ commentList.length }}）</div>

      <van-list v-model="commentLoading" :finished="commentFinished" @load="commentFetchListData">
        <div class="mt-6 flex flex-col gap-y-4">
          <div v-for="item in commentList" :key="item.id" class="col gap-y-2.5">
            <!-- 评论 -->
            <div>
              <div class="flex items-center gap-x-2">
                <img
                  :src="getFullAssetsUrl(item.createUser.avatar_url)"
                  alt=""
                  class="size-10 rounded-full"
                />
                <div class="flex flex-col gap-y-1">
                  <div class="flex items-center gap-x-1">
                    {{ item.createUser.realname || '匿名用户' }}
                    <div class="flex items-center gap-x-1.5">
                      <van-tag
                        v-for="position in item.createUser.positions"
                        :key="position"
                        :color="hexOrRgbToRgba('#37f', 0.2)"
                        text-color="#37f"
                      >
                        {{ position }}
                      </van-tag>
                    </div>
                  </div>
                  <div class="text-[#8895AB]">
                    {{ item.createUser.phone || '暂无' }}
                  </div>
                </div>
              </div>
              <div class="mt-2.5">
                {{ item.content }}
              </div>
              <div class="mt-1 flex items-center justify-between text-xs text-[#999]">
                <div>{{ item.create_time }}</div>
                <div @click="handleReply(item.id, item.createUser.realname)">
                  <Iconfont name="pinglun" size="16px" />
                </div>
              </div>
            </div>

            <!-- 回复 -->
            <div
              v-if="item.children && item.children.length"
              class="mt-1 flex flex-col gap-y-2 rounded-lg bg-[#FAF9F9] p-3"
            >
              <div v-for="reply in item.children" :key="reply.id">
                <div>
                  <span class="text-[#666]">
                    {{ reply.createUser.realname || '匿名用户' }}
                  </span>
                  <span> 回复 </span>
                  <span class="text-[#666]">
                    {{ reply.replyUser.realname || '匿名用户' }}
                  </span>
                  <span>：</span>
                  <span>
                    {{ reply.content }}
                  </span>
                </div>
                <div class="mt-1 flex items-center justify-between text-xs text-[#999]">
                  <div>{{ reply.create_time }}</div>
                  <div @click="handleReply(item.id, item.createUser.realname)">
                    <Iconfont name="pinglun" size="16px" />
                  </div>
                </div>
                <div class="my-3 h-[0.5px] bg-[#E6E6E6]" />
              </div>
            </div>

            <div
              v-if="item.child_count && (item.children?.length || 0) < item.child_count"
              class="mt-1 text-blue-500"
              @click="handleShowMoreComment(item.id)"
            >
              展开剩余{{ item.child_count - (item.children?.length || 0) }}条评论
              <van-icon name="arrow-down" />
            </div>
            <div
              v-if="item.child_count && (item.children?.length || 0) >= item.child_count"
              class="mt-1 flex items-center gap-x-1 text-blue-500"
              @click="handleHideComment(item.id)"
            >
              <div class="h-[0.5px] w-8 bg-blue-500" />
              收起 <van-icon name="arrow-up" />
            </div>
          </div>
        </div>
      </van-list>
    </div>

    <FixedBottom>
      <SendMessage
        v-model="sendMessageFocus"
        :placeholder="sendMessagePlaceholder"
        @send="handlePublicComment"
      />
    </FixedBottom>
  </div>
</template>
