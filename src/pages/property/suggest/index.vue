<script>
import { formatDate, getFullAssetsUrl } from '@/utils'
import { doneSuggestApi, getSuggestListApi, getVoteListApi } from '@/services/api/property/suggest'
import createListRefresh from '@/mixins/listRefresh'
import { DirectionEnum, IsHotEnum, VoteAnonymityEnum, VoteStatusEnum } from '@/enums/property'
import Navbar from '@/components/Navbar.vue'
import Iconfont from '@/components/Iconfont.vue'

export default {
  components: { Iconfont, Navbar },
  mixins: [
    createListRefresh(getSuggestListApi, {
      prefix: 'suggestion',
    }),
    createListRefresh(getVoteListApi, {
      prefix: 'vote',
    }),
  ],
  data() {
    return {
      IsHotEnum,
      VoteAnonymityEnum,
      DirectionEnum,
    }
  },
  methods: {
    formatDate,
    getFullAssetsUrl,

    getTag(status) {
      switch (status) {
        case VoteStatusEnum.ONGOING:
          return {
            text: '进行中',
            color: '#00B384',
          }
        case VoteStatusEnum.FINISHED:
          return {
            text: '已结束',
            color: '#C4C4C4',
          }
        default:
          return {}
      }
    },

    getUserRoleText(role) {
      switch (role) {
        case 'residents':
          return '业主'
        case 'property':
          return '物业'
        case 'committee':
          return '业委会'
        default:
          return '监管者'
      }
    },

    // 处理点赞/点踩操作
    async handleLike(id, direction) {
      const item = this.suggestionList.find((item) => item.id === id)
      if (!item) return
      this.updateLikeState(item, direction)
      await doneSuggestApi({ data_id: id, direction })
    },

    // 处理点赞/点踩的状态更新
    updateLikeState(item, direction) {
      const isUpVote = direction === DirectionEnum.UP
      const currentState = isUpVote ? 'up' : 'down'
      const oppositeState = isUpVote ? 'down' : 'up'

      // 切换当前操作的状态
      item[currentState] = !item[currentState]
      item[`${currentState}_num`] += item[currentState] ? 1 : -1

      // 如果存在相反的状态，则取消
      if (item[oppositeState]) {
        item[oppositeState] = false
        item[`${oppositeState}_num`] -= 1
      }
    },

    handleHotClick() {
      this.$dialog({
        title: '温馨提示',
        message: '当前话题参与点赞，评论人数已达到当前小区已注册业主的20%',
        confirmButtonText: '确定',
        confirmButtonColor: '#37f',
      }).then(() => {})
    },

    handleTapSuggestionItem(id) {
      this.$router.push({
        path: '/property/suggest/detail',
        query: { id },
      })
    },
  },
}
</script>

<template>
  <div>
    <Navbar title="我要建议"></Navbar>
    <van-tabs color="#37f">
      <van-tab title="建议广场">
        <van-list
          v-model="suggestionLoading"
          :finished="suggestionFinished"
          @load="suggestionFetchListData"
        >
          <div class="bg-white p-3.5">
            <div
              v-for="item in suggestionList"
              :key="item.id"
              class="flex flex-col gap-2.5 p-3"
              @click="handleTapSuggestionItem(item.id)"
            >
              <div class="flex items-center gap-x-2">
                <img
                  :src="getFullAssetsUrl(item.user?.avatar_url || '')"
                  alt=""
                  class="size-10 rounded-full"
                />
                <div>
                  <div class="font-medium">
                    {{ item.user?.realname || '匿名用户' }}
                  </div>
                  <div class="text-[#999]">
                    {{ item.create_time }}
                  </div>
                </div>
                <div
                  v-if="item.is_hot === IsHotEnum.YES"
                  class="ml-auto flex items-center text-[#F56745]"
                  @click.stop="handleHotClick"
                >
                  <Iconfont name="remen" size="40rpx" />
                  <span>热门</span>
                  <van-icon class="ml-1" color="#888889" name="question-o" size="16px" />
                </div>
              </div>

              <div v-if="item.description" class="line-clamp-2">
                {{ item.description }}
              </div>

              <div
                v-if="item.images.length"
                :class="item.images.length > 1 ? 'grid-cols-3' : 'grid-cols-2'"
                class="grid gap-x-2"
              >
                <img
                  v-for="(image, i) in item.images"
                  :key="i"
                  :src="getFullAssetsUrl(image)"
                  alt=""
                  class="w-full"
                />
              </div>

              <div class="flex items-center gap-x-4 text-[#A8A5A4]">
                <div class="flex items-center gap-x-1">
                  <Iconfont name="yanjing1" />
                  <span>{{ item.view_num }}</span>
                </div>
                <div class="flex items-center gap-x-1">
                  <Iconfont name="pinglun" />
                  <span>{{ item.disc_num }}</span>
                </div>
                <div
                  class="flex items-center gap-x-1"
                  @click.stop="handleLike(item.id, DirectionEnum.UP)"
                >
                  <Iconfont v-if="!item.up" custom-class="-mt-0.5" name="dianzan3" />
                  <Iconfont v-else color="#F56745" custom-class="-mt-0.5" name="dianzan" />
                  <span>{{ item.up_num }}</span>
                </div>
                <div
                  class="flex items-center gap-x-1"
                  @click.stop="handleLike(item.id, DirectionEnum.DOWN)"
                >
                  <Iconfont v-if="!item.down" custom-class="mt-0.5" name="dianzan1" />
                  <Iconfont v-else color="#F56745" custom-class="mt-0.5" name="dianzan2" />
                  <span>{{ item.down_num }}</span>
                </div>
              </div>

              <div class="h-[0.5px] bg-[#DCDCDC]" />

              <div v-if="item.is_hot === IsHotEnum.YES && !item.published_vote">
                <div
                  class="mx-auto h-8 w-[120px] rounded-full border border-blue-500 text-center leading-8 text-blue-500"
                >
                  <Iconfont name="bianji2" />
                  <span> 发起商量 </span>
                </div>
              </div>
            </div>
          </div>
        </van-list>
      </van-tab>
      <van-tab title="一起商量">
        <van-list v-model="voteLoading" :finished="voteFinished" @load="voteFetchListData">
          <div class="bg-white p-3.5">
            <div v-for="item in voteList" :key="item.id" class="overflow-hidden rounded p-3">
              <div class="flex gap-x-3 border-b border-neutral-100 pb-3">
                <div class="relative">
                  <img
                    :src="getFullAssetsUrl('/wxapp/property/vote_icon.png')"
                    alt=""
                    class="h-17 w-24 shrink-0 rounded"
                  />
                  <div
                    :style="{ backgroundColor: getTag(item.status).color }"
                    class="absolute left-0 top-0 flex h-[17px] w-[42px] items-center justify-center rounded-br rounded-tl text-xs text-white"
                  >
                    {{ getTag(item.status).text }}
                  </div>
                </div>
                <div class="col flex-1 justify-between">
                  <div class="line-clamp-2 text-base font-medium">
                    {{ item.title }}
                  </div>
                  <div class="flex justify-between text-xs text-[#999]">
                    <div>{{ formatDate(item.create_time) }}</div>
                    <div>
                      {{ getUserRoleText(item.user_role) }}
                      <span> ：</span>
                      {{ item.anonymity === VoteAnonymityEnum.ANONYMOUS ? '匿名' : item.user_name }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="flex items-center gap-x-4 pt-2 text-xs text-[#999]">
                <div>
                  热度
                  <span class="text-[#FF922E]">
                    {{ item.view_num }}
                  </span>
                </div>
                <div>
                  投票
                  <span class="text-[#FF922E]"> {{ item.voted_count || 0 }}人 </span>
                </div>
                <div
                  v-if="item.is_hot === IsHotEnum.YES"
                  class="flex items-center text-[#F56745]"
                  @click.stop="handleHotClick"
                >
                  <Iconfont color="#F56745" name="remen" size="40rpx" />
                  热门
                  <van-icon class="ml-1" color="#888889" name="question-o" size="16px" />
                </div>
                <!--                <div class="ml-auto">-->
                <!--                  <van-icon name="delete-o" size="16px" />-->
                <!--                </div>-->
              </div>
            </div>
          </div>
        </van-list>
      </van-tab>
    </van-tabs>
  </div>
</template>
