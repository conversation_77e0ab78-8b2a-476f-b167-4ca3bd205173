<script>
import Navbar from '@/components/Navbar.vue'
import Uploader from '@/components/Uploader.vue'

export default {
  components: {
    Navbar,
    Uploader,
  },
  data() {
    return {
      form: {
        content: '',
        images: [],
      },
    }
  },
  methods: {
    onSubmit() {},
  },
}
</script>

<template>
  <div class="min-h-screen bg-white">
    <Navbar bgColor="#fff" title="我要建议"></Navbar>
    <van-form ref="formRef" class="rounded-2 mt-4 px-4" @submit="onSubmit">
      <van-field
        v-model="form.content"
        :rules="[{ required: true }]"
        autosize
        label="内容"
        maxlength="1000"
        placeholder="请输入内容"
        required
        rows="1"
        type="textarea"
      />
      <div class="pl-2">
        <div class="mt-3">
          <div class="mb-2 flex items-center text-sm">
            <span class="text-[#666]">上传图片</span>
          </div>
        </div>
        <Uploader v-model="form.images"></Uploader>
      </div>
      <div style="margin: 16px">
        <van-button block native-type="submit" round type="info">发布</van-button>
      </div>
    </van-form>
  </div>
</template>

<style scoped>
.van-cell {
  display: flex;
  flex-direction: column;
  line-height: 30px;
  font-size: var(--text-sm);
}

.van-cell__title {
  font-size: 14px;
  color: #333333;
}
</style>
