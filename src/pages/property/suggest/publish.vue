<script>
import Uploader from '@/components/Uploader.vue'
import FixedBottom from '@/components/FixedBottom.vue'

export default {
  components: {
    FixedBottom,
    Uploader,
  },
  data() {
    return {
      form: {
        content: '',
        images: [],
      },
    }
  },
  methods: {
    onSubmit() {},
  },
}
</script>

<template>
  <div class="h-full p-3.5">
    <van-form ref="formRef" class="overflow-hidden rounded-md" @submit="onSubmit">
      <van-field
        v-model="form.content"
        :autosize="{ minHeight: 170 }"
        :border="false"
        label="内容"
        maxlength="500"
        placeholder="请输入内容"
        required
        type="textarea"
      >
      </van-field>

      <van-field label="上传图片" name="uploader">
        <template #input>
          <Uploader v-model="form.images"></Uploader>
        </template>
      </van-field>
    </van-form>

    <FixedBottom>
      <van-button block class="btn-property" native-type="submit">提交</van-button>
    </FixedBottom>
  </div>
</template>

<style lang="scss" scoped>
:deep(.van-cell) {
  display: flex;
  flex-direction: column;

  textarea {
    border-radius: 6px;
    padding: 12px;
    background-color: #f5f5f5;
  }
}

:deep(.van-field__control) {
  margin-top: 8px;
}
</style>
