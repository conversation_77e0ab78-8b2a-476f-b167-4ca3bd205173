<script>
import Navbar from '@/components/Navbar.vue'
import { getVoteCateListApi } from '@/services/api/property/community'
import { createVoteApi } from '@/services/api/property/suggest'
import store from '@/store'

export default {
  components: {
    Navbar,
  },
  data() {
    return {
      id: this.$route.query.id,
      commDeciDetail: null,
      form: {
        suggest_id: this.$route.query.id,
        cate_id: '',
        title: this.$route.query.title,
        description: '',
        end_time: '',
        anonymity: 0,
        items: [{ value: '' }, { value: '' }, { value: '' }],
      },
      cate: '',
      columns: [],
      flatColumns: [],
      showCate: false,
      currentDate: new Date(),
      showTime: false,
      username: '张三',
      switchChecked: false,
      userInfo: {},
    }
  },
  computed: {},
  async created() {
    console.log(this.$route, 'this.$route')
    this.userInfo = store.state.user.userInfo
    const res = await getVoteCateListApi()
    this.columns = res
    this.flatColumns = this.flattenTree(res)
  },
  methods: {
    getTimeStamp(timeStr) {
      if (!timeStr) return Date.now()
      let t = timeStr
      t = t > 0 ? +t : t.toString().replace(/-/g, '/')
      return new Date(t).getTime()
    },
    onSubmit() {
      const values = this.form.items.map((item) => item.value.trim())
      const flag = values.every((item) => item.trim() !== '')
      if (!flag) return this.$toast({ message: '表决选项不能为空', icon: 'none' })
      if (new Set(values).size !== values.length)
        return this.$toast({ message: '表决选项不能重复', icon: 'none' })
      createVoteApi({
        ...this.form,
        end_time: `${this.getTimeStamp(this.form.end_time) / 1000}`,
      })
      this.$toast.success('发布成功')
      this.$route.params.index = 1
      this.$router.back()
    },

    handleTypeClick() {
      console.log(123)
    },
    // 类型
    onConfirm(e) {
      const res = this.flatColumns.find((item) => item.title === e[1])
      this.form.cate_id = res.value
      this.cate = res.title
      this.showCate = false
    },
    flattenTree(tree, parent = null) {
      let result = []
      tree.forEach((node) => {
        // 添加当前节点的信息（可以根据需要添加更多属性）
        result.push({ ...node, parent })
        // 如果节点有子节点，递归处理子节点
        if (node.children) {
          result = result.concat(this.flattenTree(node.children, node))
        }
      })

      return result
    },

    // 时间
    onConfirmTime(e) {
      const date = new Date(e)
      const year = date.getFullYear().toString() // 取后两位年份
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，所以要+1并补零
      const day = date.getDate().toString().padStart(2, '0') // 日期补零
      const hour = date.getHours().toString().padStart(2, '0') // 小时补零
      const minute = date.getMinutes().toString().padStart(2, '0') // 分钟补零

      this.form.end_time = `${year}/${month}/${day} ${hour}:${minute}`
      this.showTime = false
    },
  },
}
</script>

<template>
  <div class="min-h-screen bg-[#e4f2fd]">
    <Navbar bgColor="#e4f2fd" title="发起商量"></Navbar>
    <van-form @submit="onSubmit" ref="formRef" class="rounded-2 mt-4 px-4">
      <van-field
        v-model="cate"
        required
        label="类型"
        placeholder="请选择类型"
        :rules="[{ required: true }]"
        right-icon="arrow"
        readonly
        @click="showCate = true"
      />
      <van-field
        v-model="form.title"
        required
        rows="1"
        autosize
        label="商量事项"
        type="textarea"
        placeholder="请输入，500字以内"
        maxlength="500"
        :rules="[{ required: true }]"
      />
      <van-field
        v-model="form.end_time"
        required
        label="结束时间"
        placeholder="请选择结束时间"
        :rules="[{ required: true }]"
        right-icon="arrow"
        readonly
        @click="showTime = true"
      />

      <div class="bg-white">
        <!-- <div class="flex pl-3 pt-4 text-sm text-[#646566]">
          <div>发起人</div>
          <span class="ml-2" style="font-size: 12px"> (匿名) </span>
          <van-switch
            class="ml-2"
            v-model="form.anonymity"
            :inactive-value="0"
            :active-value="1"
            size="20"
          />
        </div> -->
        <van-field label-width="100%" readonly v-model="userInfo.realname" label="">
          <template #label>
            <div class="flex text-sm text-[#646566]">
              <div>发起人</div>
              <span class="ml-2" style="font-size: 12px"> (匿名) </span>
              <van-switch
                class="ml-2"
                v-model="form.anonymity"
                :inactive-value="0"
                :active-value="1"
                size="20"
              />
            </div>
          </template>
        </van-field>
      </div>

      <van-field
        v-model="form.description"
        rows="1"
        autosize
        maxlength="1000"
        label="其他说明"
        type="textarea"
        placeholder="请输入，1000字以内"
      />

      <div class="select bg-white pb-4">
        <div class="flex pl-3 pt-4 text-sm text-[#646566]">
          <div><span class="text-[red]">*</span>表决事项</div>
        </div>
        <van-field
          v-for="(item, indexs) in form.items"
          :key="indexs"
          v-model="form.items[indexs].value"
          :label="String(indexs + 1).padStart(2, '0')"
          placeholder="请输入"
          label-width="20"
          maxlength="500"
        >
          <template #button>
            <van-button
              size="small"
              @click="form.items.splice(indexs, 1)"
              class="text-sm text-[red]"
              type="text"
              >删除</van-button
            >
          </template>
        </van-field>
        <van-button
          plain
          block
          icon="plus"
          @click="form.items.push({ value: '' })"
          style="width: 90%; margin: 0 auto; border-radius: 10px; font-size: var(--text-base)"
          type="info"
          >继续添加</van-button
        >
      </div>

      <div style="margin: 16px">
        <van-button native-type="submit" round block type="info">发布</van-button>
      </div>
    </van-form>

    <!-- 选择类型 -->
    <van-popup v-model="showCate" position="bottom">
      <van-picker
        show-toolbar
        value-key="title"
        :columns="columns"
        @confirm="onConfirm"
        @cancel="showCate = false"
      >
      </van-picker>
    </van-popup>

    <!-- 选择时间 -->
    <van-popup v-model="showTime" position="bottom">
      <van-datetime-picker
        @confirm="onConfirmTime"
        @cancel="showTime = false"
        v-model="currentDate"
        type="datetime"
      />
    </van-popup>
  </div>
</template>

<style scoped>
.van-form {
  border-radius: 10px;
}
.van-cell {
  display: flex;
  flex-direction: column;
  line-height: 30px;
  font-size: var(--text-sm);
}
.van-cell__title {
  font-size: 14px;
  color: #333;
}
.van-field__right-icon {
  color: #c8c9cc !important;
}

.select .van-cell {
  display: flex;
  flex-direction: row;
}
</style>
