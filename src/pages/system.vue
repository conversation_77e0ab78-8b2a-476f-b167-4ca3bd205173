<script>
import { mapActions } from 'vuex'
import { getFullAssetsUrl } from '@/utils'
import elderModeMixin from '@/mixins/elderMode'
import FixedBottom from '@/components/FixedBottom.vue'

export default {
  mixins: [elderModeMixin],
  components: {
    FixedBottom,
  },
  data() {
    return {
      systemList: [
        {
          label: '阳光物业',
          value: 'property',
          image: getFullAssetsUrl('/wxapp/common/start_property.png'),
          textColor: '#3377FF',
          path: '/property',
        },
        {
          label: '商贸中心',
          value: 'business',
          image: getFullAssetsUrl('/wxapp/common/start_shopping.png'),
          textColor: '#E0322D',
        },
        {
          label: '农民工安薪',
          value: 'worker',
          image: getFullAssetsUrl('/wxapp/common/start_peasants.png'),
          textColor: '#CB4900',
        },
        {
          label: '预付费监管',
          value: 'supervision',
          image: getFullAssetsUrl('/wxapp/common/start_prepayment.png'),
          textColor: '#333333',
        },
      ],
      selectedSystem: 'property',
    }
  },
  computed: {
    currentSystem() {
      return this.systemList.find((item) => item.value === this.selectedSystem)
    },
  },
  mounted() {
    this.loginAction()
  },
  methods: {
    ...mapActions('user', ['loginAction']),

    selectSystem(system) {
      this.selectedSystem = system
    },

    // 确定
    confirmSelection() {
      if (this.currentSystem.path) {
        this.$router.push(this.currentSystem.path)
      }
    },
  },
}
</script>

<template>
  <div class="h-screen bg-white">
    <!-- 导航栏 -->
    <van-nav-bar :border="false" safe-area-inset-top>
      <template #left>
        <span class="title">透明嘉</span>
      </template>
    </van-nav-bar>

    <div class="p-3.5">
      <div>
        <p class="text-xl font-bold">请选择版本</p>
        <p class="my-2 text-sm leading-8">
          因在系统中您的账号兼任多个角色，我们为您提供4种版本，便于我们提供最佳服务
        </p>
      </div>

      <!-- 选择系统 -->
      <div class="flex flex-col gap-y-5">
        <div
          v-for="item in systemList"
          :key="item.value"
          :class="{ 'card-selected': selectedSystem === item.value }"
          :style="{ backgroundImage: `url(${item.image})` }"
          class="card flex items-center justify-center rounded-lg border-2 border-dashed border-transparent"
          @click="selectSystem(item.value)"
        >
          <span :style="{ color: item.textColor }" class="font-500 text-xl">
            {{ item.label }}
          </span>
        </div>
      </div>

      <FixedBottom>
        <van-button class="btn-property" @click="confirmSelection"> 确认 </van-button>
      </FixedBottom>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.title {
  font-size: 22px;
  font-weight: 700;
  color: #333333;
}

.card {
  height: 115px;
  background-position: left top;
  background-repeat: no-repeat;
  background-size: cover;

  &-selected {
    @apply border-orange-500;
  }
}
</style>
