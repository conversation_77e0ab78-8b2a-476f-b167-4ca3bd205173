import Vue from 'vue'

// 导入需要使用的组件
import {
  Button,
  Cell,
  CellGroup,
  Dialog,
  Field,
  Form,
  Icon,
  Image as VanImage,
  IndexAnchor,
  IndexBar,
  NavBar,
  Popup,
  Swipe,
  SwipeItem,
  Tabs,
  Tab,
  Tabbar,
  TabbarItem,
  Toast,
  NoticeBar,
  Picker,
  Popover,
  List,
  DatetimePicker,
  PullRefresh,
  Search,
  Progress,
} from 'vant'

// 组件列表
const components = [
  Button,
  Cell,
  CellGroup,
  Icon,
  VanImage,
  Field,
  Form,
  Popup,
  NavBar,
  Tabbar,
  TabbarItem,
  Swipe,
  SwipeItem,
  IndexBar,
  IndexAnchor,
  NoticeBar,
  Picker,
  Popover,
  List,
  Tabs,
  Tab,
  DatetimePicker,
  PullRefresh,
  Search,
  Progress,
]

// 需要挂载到Vue原型的方法
const prototypes = {
  $toast: Toast,
  $dialog: Dialog,
}

// 批量注册组件
components.forEach((component) => {
  Vue.use(component)
})

// 批量挂载原型方法
Object.entries(prototypes).forEach(([key, value]) => {
  Vue.prototype[key] = value
})

// 单独注册有特殊配置的组件
Vue.use(Toast)
Vue.use(Dialog)
