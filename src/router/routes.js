export default [
  {
    path: '/',
    redirect: '/system',
  },
  {
    path: '/system',
    name: 'system',
    component: () => import('@/pages/system.vue'),
  },
  {
    path: '/property',
    name: 'property',
    redirect: '/property/home',
    component: () => import('@/layouts/property.vue'),
    children: [
      {
        path: 'home',
        component: () => import('@/pages/property/home.vue'),
      },
    ],
  },
  {
    path: '/property/community/information',
    component: () => import('@/pages/property/community/information.vue'),
  },
  {
    path: '/property/community/list',
    component: () => import('@/pages/property/community/list.vue'),
  },
  {
    path: '/property/community/committee',
    component: () => import('@/pages/property/community/committee.vue'),
  },
  {
    path: '/property/community/property',
    component: () => import('@/pages/property/community/property.vue'),
  },
  {
    path: '/property/community/property-member',
    component: () => import('@/pages/property/community/property-member.vue'),
  },
  {
    path: '/property/community/committee-member',
    component: () => import('@/pages/property/community/committee-member.vue'),
  },
  {
    path: '/property/ranking',
    component: () => import('@/pages/property/ranking.vue'),
  },
  {
    path: '/property/finance/bank',
    component: () => import('@/pages/property/finance/bank/index.vue'),
  },
  {
    path: '/property/finance/bank/list',
    component: () => import('@/pages/property/finance/bank/list.vue'),
  },
  {
    path: '/property/finance/bank/water',
    component: () => import('@/pages/property/finance/bank/water.vue'),
  },
  {
    path: '/property/finance/bank/chart',
    component: () => import('@/pages/property/finance/bank/chart.vue'),
  },
  {
    path: '/property/finance/bank/query',
    component: () => import('@/pages/property/finance/bank/query.vue'),
  },
  {
    path: '/property/finance/query/publish',
    component: () => import('@/pages/property/finance/query/publish.vue'),
  },
  {
    path: '/property/payment/case',
    component: () => import('@/pages/property/payment/case.vue'),
  },

  {
    path: '/property/bulletin/policy',
    component: () => import('@/pages/property/bulletin/policy.vue'),
  },
  {
    path: '/property/bulletin/notice/:id',
    component: () => import('@/pages/property/bulletin/notice.vue'),
  },
  {
    path: '/property/bulletin/publicity/:id',
    component: () => import('@/pages/property/bulletin/publicity.vue'),
  },
  {
    path: '/property/suggest',
    component: () => import('@/pages/property/suggest/index.vue'),
  },
  {
    path: '/property/vote/index',
    component: () => import('@/pages/property/vote/index.vue'),
  },
  {
    path: '/property/suggest/detail',
    component: () => import('@/pages/property/suggest/detail.vue'),
  },
  {
    path: '/property/suggest/publish',
    component: () => import('@/pages/property/suggest/publish.vue'),
  },
]
