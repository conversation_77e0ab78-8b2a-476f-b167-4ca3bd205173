import { request } from '@/services/request'
// import {zlbRequest} from '@/services/request'
import { apiPrefix } from '@/utils'

const Apis = {
  ZLB_LOGIN: `${apiPrefix}/auth/zlbLogin`,
  UPDATE_USER_INFO: `${apiPrefix}/profile/update`,
}

// const MgopApis = {
//   ZLB_LOGIN: 'mgop.tenqent.jxtmj.dologin',
// }

/**
 * @description 登录（浙里办）
 * @param {String} ticketId
 * @returns
 */
// export const zlbLoginApi = (ticketId) => zlbRequest.post(MgopApis.ZLB_LOGIN, { ticketId })

/**
 * @description 登录）
 * @param {String} ticketId
 * @returns
 */
export const zlbLoginApi = (ticketId) => request.post(Apis.ZLB_LOGIN, { ticketId })

/**
 * @description: 更新用户信息
 * @param {Object} data
 * @return
 */
export const updateUserInfoApi = (data) => request.post(Apis.UPDATE_USER_INFO, data)
