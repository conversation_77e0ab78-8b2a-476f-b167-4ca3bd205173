import { request } from '@/services/request'
import { apiPrefix } from '@/utils'

const Apis = {
  COMMENT_LIST: `${apiPrefix}/sunshine.Comment/index`,
  COMMENT_PUBLIC: `${apiPrefix}/sunshine.Comment/storage`,
  COMMENT_DELETE: `${apiPrefix}/sunshine.Comment/delete`,
  COMMENT_LIKE: `${apiPrefix}/sunshine.Comment/like`,
}

/**
 * @description: 评论列表
 * @param {object} params
 * @param {number} params.page
 * @param {number} params.limit
 * @param {number} params.data_id 数据id
 * @param {number} params.parent_id 父级id
 * @param {string} params.topic 评论类型 'notice' | 'bill' | 'vote' | 'resolution' | 'suggest' | 'tender'
 * @return
 */
export const getCommentListApi = (params) => request.get(Apis.COMMENT_LIST, params)

/**
 * @description: 评论发布
 * @param {object} data
 * @param {number} data.data_id 数据id
 * @param {number} data.parent_id 父级id
 * @param {string} data.content 评论内容
 * @param {string} data.images 评论图片
 * @param {string} data.topic 评论类型 'notice' | 'bill' | 'vote' | 'resolution' | 'suggest' | 'tender'
 * @return
 */
export const publicCommentApi = (data) => request.post(Apis.COMMENT_PUBLIC, data)

/**
 * @description: 评论删除
 * @param {number} comment_id
 * @return
 */
export const deleteCommentApi = (comment_id) => request.post(Apis.COMMENT_DELETE, { comment_id })

/**
 * @description: 评论点赞
 * @param {number} data_id
 * @return
 */
export const giveCommentLikeApi = (data_id) => request.post(Apis.COMMENT_LIKE, { data_id })
