import { request } from '@/services/request'
import { apiPrefix } from '@/utils'

const Apis = {
  STREET_COMMUNITY_LIST: `${apiPrefix}/sunshine.community/jxStreetCommunity`,
  COMMUNITY_INFO: `${apiPrefix}/sunshine.Community/detail`,
  COMMITTEE_INFO: `${apiPrefix}/sunshine.Community/committee`,
  PROPERTY_INFO: `${apiPrefix}/sunshine.Community/property`,
  COMMITTEE_MEMBER: `${apiPrefix}/sunshine.Community/committeeDetail`,
  PROPERTY_RANKING: `${apiPrefix}/sunshine.temp/rank`,
  VOTE_CATE_LIST: `${apiPrefix}/sunshine.vote/cates`,
}

/**
 * @description 获取小区列表（带街道分类）
 * @returns {Promise<unknown>}
 */
export const getStreetCommunityListApi = () => request.get(Apis.STREET_COMMUNITY_LIST)

/**
 * @description 获取小区信息
 * @returns {Promise | Promise<unknown>}
 */
export const getCommunityInfoApi = () => request.get(Apis.COMMUNITY_INFO)

/**
 * @description 获取业委会信息
 * @returns {Promise<any>}
 */
export const getCommitteeInfoApi = () => request.get(Apis.COMMITTEE_INFO)

/**
 * @description 获取物业信息
 * @returns {Promise | Promise<unknown>}
 */
export const getPropertyInfoApi = () => request.get(Apis.PROPERTY_INFO)

/**
 * @description 获取业委会成员信息
 * @param id 业委会成员id
 * @returns {Promise | Promise<unknown>}
 */
export const getCommitteeMemberApi = (id) => request.get(Apis.COMMITTEE_MEMBER, { id })

/**
 * @description 获取物业红黑榜
 * @param {*} streetCode  街道code
 * @returns
 */
export const getPropertyRankingApi = (streetCode) => {
  return request.get(Apis.PROPERTY_RANKING, { streetCode })
}

/**
 * @description: 调研分类列表
 */
export const getVoteCateListApi = () => request.get(Apis.VOTE_CATE_LIST)
