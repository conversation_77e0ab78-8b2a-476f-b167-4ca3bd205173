import { request } from '@/services/request'
import { apiPrefix } from '@/utils'

const Apis = {
  FINANCE_ACCOUNT_IDS: `${apiPrefix}/sunshine.finance/accountIds`,
  FINANCE_CHART_DATA: `${apiPrefix}/sunshine.finance/chartData`,
  FINANCE_CATE_BILLS: `${apiPrefix}/sunshine.finance/cateBills`,
  FINANCE_BILL: `${apiPrefix}/sunshine.finance/bill`,
  FINANCE_CATE: `${apiPrefix}/sunshine.finance/cate`,
  FINANCE_STAT: `${apiPrefix}/sunshine.finance/stat`,
  FINANCE_DETAIL: `${apiPrefix}/sunshine.finance/detail`,

  QUESTION_LIST: `${apiPrefix}/sunshine.Doubt/list`,
  QUESTION_DETAIL: `${apiPrefix}/sunshine.Doubt/detail`,
  QUESTION_PUBLISH: `${apiPrefix}/sunshine.Doubt/storage`,
  QUESTION_DELETE: `${apiPrefix}/sunshine.Doubt/delete`,
  QUESTION_RECORD: `${apiPrefix}/sunshine.Doubt/bankBillRecords`,
  QUESTION_HANDLE: `${apiPrefix}/sunshine.Doubt/deal`,
}

/**
 * @description 获取财务帐号
 * @returns
 */
export const getFinanceAccountApi = () => request.get(Apis.FINANCE_ACCOUNT_IDS)

/**
 * @description: 账单分类列表
 * @return {*}
 */
export const getFinanceBillCateListApi = (params) => request.get(Apis.FINANCE_CATE_BILLS, params)

/**
 * @description 获取财务图表数据
 * @param params
 * @returns
 */
export const getFinanceChartDataApi = (params) => request.get(Apis.FINANCE_CHART_DATA, params)

/**
 * @description: 获取银行流水账单数据
 * @param {Object} params
 * @return {*}
 */
export const getBankBillDataApi = (params) => request.get(Apis.FINANCE_BILL, params)

/**
 * @description: 获取分类筛选数据
 * @param {String} account_id 账户id
 * @return {*}
 */
export const getFinanceCateApi = (account_id) => request.get(Apis.FINANCE_CATE, { account_id })

/**
 * @description: 获取统计分析
 * @param {String} year
 * @return {*}
 */
export const getFinanceStatApi = (year) => request.get(Apis.FINANCE_STAT, { year })

/**
 * @description: 账单详情
 * @param {Object} params
 * @param {String | Number} params.id
 * @param {String | Number} params.account_id 账户id
 * @return {*}
 */
export const getFinanceDetailApi = (params) => request.get(Apis.FINANCE_DETAIL, params)

/**
 * @description: 质疑列表
 * @param {Object} params
 * @return
 */
export const getQuestionListApi = (params) => request.get(Apis.QUESTION_LIST, params)

/**
 * @description: 发布质疑
 * @param {Object} params
 * @returns {Promise | Promise<unknown>}
 */
export const publishQuestionApi = (params) => request.post(Apis.QUESTION_PUBLISH, params)
