import { request } from '@/services/request'
import { apiPrefix } from '@/utils'

const Apis = {
  SUGGEST_DONE: `${apiPrefix}/sunshine.Suggest/done`,
  SUGGEST_PUBLISH: `${apiPrefix}/sunshine.Suggest/save`,
  SUGGEST_DELETE: `${apiPrefix}/sunshine.Suggest/delete`,
  SUGGEST_LIST: `${apiPrefix}/sunshine.Suggest/list`,
  SUGGEST_DETAIL: `${apiPrefix}/sunshine.Suggest/detail`,
  VOTE_LIST: `${apiPrefix}/sunshine.vote/list`,
  VOTE_DETAIL: `${apiPrefix}/sunshine.vote/detail`,
  VOTE_CREATE: `${apiPrefix}/sunshine.vote/storage`,
  VOTE_DELETE: `${apiPrefix}/sunshine.vote/delete`,
  VOTE_SUBMIT: `${apiPrefix}/sunshine.vote/vote`,
}

/**
 * @description: 建议列表
 * @param params
 * @returns {Promise | Promise<unknown>}
 */
export const getSuggestListApi = (params) => request.get(Apis.SUGGEST_LIST, params)

/**
 * @description: 发布建议
 * @param params
 * @returns {Promise | Promise<unknown>}
 */
export const publishSuggestApi = (params) => request.post(Apis.SUGGEST_PUBLISH, params)

/**
 * @description: 删除建议
 * @param id
 * @returns {Promise | Promise<unknown>}
 */
export const deleteSuggestApi = (id) => request.post(Apis.SUGGEST_DELETE, { id })

/**
 * @description: 意见点赞/点踩
 * @param {object} params
 * @return
 */
export const doneSuggestApi = (params) => request.post(Apis.SUGGEST_DONE, params)

/**
 * @description: 建议详情
 * @param id
 * @returns {Promise | Promise<unknown>}
 */
export const getSuggestDetailApi = (id) => request.get(Apis.SUGGEST_DETAIL, { id })

/**
 * @description: 投票列表
 * @param params
 * @returns {Promise | Promise<unknown>}
 */
export const getVoteListApi = (params) => request.get(Apis.VOTE_LIST, params)

/**
 * @description: 投票详情
 * @param id
 * @returns {Promise | Promise<unknown>}
 */
export const getVoteDetailApi = (id) => request.get(Apis.VOTE_DETAIL, { id })

/**
 * @description: 投票创建
 * @param params
 * @returns {Promise | Promise<unknown>}
 */
export const createVoteApi = (params) => request.post(Apis.VOTE_CREATE, params)

/**
 * @description: 投票删除
 * @param id
 * @returns {Promise | Promise<unknown>}
 */
export const deleteVoteApi = (id) => request.post(Apis.VOTE_DELETE, { id })

/**
 * @description: 投票提交
 * @param params
 * @returns {Promise | Promise<unknown>}
 */
export const submitVoteApi = (params) => request.post(Apis.VOTE_SUBMIT, params)
