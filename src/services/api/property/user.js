import { request } from '@/services/request'
import { apiPrefix } from '@/utils'

const Apis = {
  USER_INFO: `${apiPrefix}/sunshine.user/profile`,
  HOUSE_OWNER_SHIPS: `${apiPrefix}/sunshine.user/houseOwnerships`,
}

/**
 * @description 用户信息
 * @param console 用户身份
 * @returns
 */
export const getPropertyUserInfoApi = (console) => request.get(Apis.USER_INFO, { console })

/**
 * @description 获取不动产权证的房产信息
 * @returns {Promise<any>}
 */
export const getHouseOwnershipsApi = () => request.get(Apis.HOUSE_OWNER_SHIPS)
