import { get, post } from '../axios'
import { QuestionListParams, QuestionListResult } from './types'

export const getQuestionListApi = (params?: QuestionListParams) => {
  return get<QuestionListResult>('/api/question', params)
}

export const getQuestionDetailApi = (id: string | number) => {
  return get(`/api/question/${id}`)
}

export const createQuestionApi = (data: Record<string, any>) => {
  return post('/api/question', data)
}
