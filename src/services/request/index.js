import store from '@/store'
import Axios from './Axios'
import MgopRequest from './Mgop'
import { apiPrefix, baseUrl, useProxy } from '@/utils'

const baseURL = useProxy ? apiPrefix : baseUrl

// 正常 axios 请求
export const request = new Axios({
  baseURL,
  headers: {
    server: 1,
    communityid: '1',
  },
  interceptors: {
    requestInterceptors: (config) => {
      const token = store.state.user.token
      const communityId = store.state.user.communityId
      config.headers = {
        communityId: communityId,
        Authorization: `Bearer ${token}`,
        ...config.headers,
      }

      return config
    },
    responseInterceptors: (response) => {
      if (response.code === 1) {
        return response.data
      } else {
        Promise.reject(response)
      }
    },
  },
})

// 接入浙里办请求
export const zlbRequest = new MgopRequest({})
