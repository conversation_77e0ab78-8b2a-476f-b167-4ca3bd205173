import { updateUserInfoApi, zlbLogin<PERSON><PERSON> } from '@/services/api/common/user'
import { getPropertyUserInfoApi } from '@/services/api/property/user'

// 用户模块
const user = {
  namespaced: true,

  state: {
    token: '',
    userInfo: null, // 用户信息
    communityId: '', // 用户当前选择的小区id
    propertyUserInfo: null, // 阳光物业用户信息
  },

  getters: {
    // 上一次选择的小区id
    lastCommunityId: (state) => state.userInfo?.page_location?.communityId,

    // 小区名称
    communityName: (state) => state.propertyUserInfo?.sunshine?.community_name,

    // 是否认证
    isVerified: (state) => state.propertyUserInfo?.sunshine.verified,

    // 是否居民身份
    isResident: (state) => {
      return state.propertyUserInfo?.sunshine.console === 'residents'
    },

    // 是否物业身份
    isProperty(state, getters) {
      const sunshine = state.propertyUserInfo?.sunshine
      return sunshine?.console === 'property' && getters.isVerified
    },

    // 是否业委会身份
    isCommittee(state, getters) {
      const sunshine = state.propertyUserInfo?.sunshine
      return sunshine?.console === 'committee' && getters.isVerified
    },

    // 是否监察者身份
    isSupervisor(state, getters) {
      const sunshine = state.propertyUserInfo?.sunshine
      return sunshine?.console === 'supervision' && getters.isVerified
    },
  },

  mutations: {
    // 设置用户信息
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo
    },

    // 设置token
    SET_TOKEN(state, token) {
      state.token = token
    },

    // 设置小区id
    SET_COMMUNITY_ID(state, communityId) {
      state.communityId = communityId
    },

    // 设置阳光物业用户信息
    SET_PROPERTY_PROFILE(state, userInfo) {
      state.propertyUserInfo = userInfo
    },

    SET_PERMISSIONS(state, permissions) {
      state.permissions = permissions
    },

    SET_LOGIN_ERROR(state, error) {
      state.loginError = error
      state.isLoggedIn = false
    },

    CLEAR_USER_INFO(state) {
      state.userInfo = null
      state.isLoggedIn = false
      state.permissions = []
      state.loginError = null
    },
  },

  actions: {
    /**
     * @description 处理浙里办单点登录
     * @returns {Promise<void>}
     */
    async loginAction({ commit }) {
      try {
        // // 检查 ZWJSBridge 是否可用
        // if (!ZWJSBridge?.ssoTicket) {
        //   throw new Error('当前环境不支持 ZWJSBridge API')
        // }

        // // 获取 SSO ticket
        // const ssoFlag = await ZWJSBridge.ssoTicket()

        // if (!ssoFlag?.result) {
        //   throw new Error('不支持"浙里办"统一单点登录组件')
        // }

        // if (!ssoFlag.ticketId) {
        //   throw new Error('获取 SSO ticket 失败')
        // }

        const res = await zlbLoginApi('debug_tk_e4a0dc3fcc8d464ba336b9bcb1ba2072')
        console.log(res, '-0res')
        commit('SET_USER_INFO', res.profile)
        commit('SET_TOKEN', res.token)
        console.log('🚀 ~ loginAction ~ res:', res)
      } catch (error) {
        console.log('🚀 ~ loginAction ~ error:', error)
        // console.error('登录失败:', error.message)
        commit('SET_LOGIN_ERROR', error.message)
        // throw error
      }
    },

    /**
     * @description 更新用户信息
     * @param data
     * @returns {Promise<void>}
     */
    async updateUserInfoAction({ commit }, data) {
      const res = await updateUserInfoApi(data)
      commit('SET_USER_INFO', res.profile)
    },

    /**
     * @description 获取阳光物业用户信息
     * @param identity 用户身份
     * @returns {Promise<void>}
     */
    async fetchPropertyUser({ commit }, identity = 'residents') {
      const userInfo = await getPropertyUserInfoApi(identity)
      commit('SET_PROPERTY_PROFILE', userInfo)
    },

    /**
     * @description 用户切换小区
     * @param id 小区id
     * @param identity 用户身份 residents/property/committee/supervision
     */
    async setCommunityAction({ dispatch, commit }, id, identity = 'residents') {
      commit('SET_COMMUNITY_ID', id)
      await dispatch('updateUserInfoAction', id)
      await dispatch('fetchPropertyUser', identity)
    },
  },
}

export default user
