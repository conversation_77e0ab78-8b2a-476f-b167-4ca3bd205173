@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-size: var(--text-sm);
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  color: #333333;
  background-color: #f5f5f5;
}

.btn {
  border-radius: 6px;
  width: 100%;
  height: 46px;
  font-size: 14px;
  color: #ffffff;
}

.btn-property {
  @extend .btn;

  background: linear-gradient(90deg, #3377ff 4%, #3399ff 101%);
}

.card {
  @apply bg-white p-3 rounded-lg overflow-hidden;
}

.row-group {
  @apply flex flex-col gap-y-3;
}

.row {
  @apply flex items-start gap-x-2 text-sm;

  & > span:first-of-type {
    @apply shrink-0 text-[#999];
  }

  & > span:last-of-type {
    @apply flex-1 text-justify;
  }
}

.text-title {
  @apply text-base font-medium;
}
