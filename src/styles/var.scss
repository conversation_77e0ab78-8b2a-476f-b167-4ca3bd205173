// 全局CSS变量定义
:root {
  // 字体大小变量
  --text-xs: 12px;
  --text-sm: 14px;
  --text-base: 16px;
  --text-lg: 18px;
  --text-xl: 20px;
  --text-2xl: 24px;
  --text-3xl: 30px;
  --text-4xl: 36px;

  // 行高
  --text-xs-line-height: calc(1 / 0.75);
  --text-sm-line-height: calc(1.25 / 0.875);
  --text-base-line-height: calc(1.5 / 1);
  --text-lg-line-height: calc(1.75 / 1.125);
  --text-xl-line-height: calc(1.75 / 1.25);
  --text-2xl-line-height: calc(2 / 1.5);
  --text-3xl-line-height: calc(2.25 / 1.875);
  --text-4xl-line-height: calc(2.5 / 2.25);
}

// 适老版CSS变量覆盖
.elder-mode {
  --text-xs: 18px;
  --text-sm: 21px;
  --text-base: 22px;
  --text-lg: 26px;
  --text-xl: 30px;
  --text-2xl: 34px;
  --text-3xl: 38px;
  --text-4xl: 42px;

  // 适老版行高变量
  --text-xs-line-height: calc(1 / 0.75);
  --text-sm-line-height: calc(1.25 / 0.875);
  --text-base-line-height: calc(1.5 / 1);
  --text-lg-line-height: calc(1.75 / 1.125);
  --text-xl-line-height: calc(1.75 / 1.25);
  --text-2xl-line-height: calc(2 / 1.5);
  --text-3xl-line-height: calc(2.25 / 1.875);
  --text-4xl-line-height: calc(2.5 / 2.25);
}
