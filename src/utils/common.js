import { isHttp, isNumber, isString } from './is'
import { baseUrl } from './env'
import { Toast } from 'vant'

/**
 * @description 获取完整的静态资源地址
 * @param {String|Array<String>} url 静态资源地址
 * @returns { String|Array<String> }
 */
export const getFullAssetsUrl = (url) => {
  if (!url) return ''

  if (isString(url)) {
    return isHttp(url) ? url : baseUrl + url
  } else {
    throw new Error('url must be string')
  }
}

/**
 * @description 获取本地图片
 * @param {string} name 图片名称
 * @param {string} suffix  图片后缀
 * @returns {string}
 */
export function getLocalizedImage(name, suffix = 'png') {
  return require(`@/assets/images/${name}.${suffix}`)
}

/**
 * @description 根据文件 URL 提取文件名和文件后缀
 * @param {String} url 文件的完整 URL
 * @returns {Object} 包含文件名和文件后缀的对象
 */
export function extractFileNameAndExtension(url) {
  // 找到最后一个 '.' 的位置
  const lastLen = url.lastIndexOf('.')

  // 根据规则提取文件名
  const fileNameWithExtension =
    url
      .slice(0, lastLen - 40)
      .split('/')
      .at(-1) || ''

  // 提取文件后缀
  const extensionMatch = url.match(/\.([a-zA-Z0-9]+)$/)
  const extension = extensionMatch ? extensionMatch[1] : ''
  // 返回结果
  return {
    fileName: fileNameWithExtension.replace(/\.[^/.]+$/, ''), // 去掉后缀的文件名
    extension,
  }
}

/**
 * @description 根据文件 URL 获取文件图标
 * @param url
 * @returns {String|Array<String>}
 */
export function getFileIconFromUrl(url) {
  const { extension } = extractFileNameAndExtension(url)
  const extResult = extension.toLowerCase()

  // 图片文件
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(extResult)) {
    return getLocalizedImage('image')
  }

  // office 文件后缀和 文件 icon 映射
  const officeIconMap = new Map([
    ['word', ['doc', 'docx']],
    ['excel', ['xls', 'xlsx']],
    ['pdf', ['pdf']],
    ['ppt', ['ppt', 'pptx']],
  ])

  for (const [key, value] of officeIconMap.entries()) {
    if (value.includes(extResult)) {
      return getLocalizedImage(key)
    }
  }
  // 其他文件
  return getLocalizedImage('file')
}

/**
 * @description 文件预览
 * @param url 文件完整 url
 */
export function previewFile(url) {
  if (!url) {
    Toast.fail('文件地址无效')
    return
  }

  const { extension } = extractFileNameAndExtension(url)

  const lowerExtension = extension.toLowerCase()

  const officeExits = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']

  if (lowerExtension === 'pdf') {
    window.open(url, '_blank')
  } else if (officeExits.includes(lowerExtension)) {
    const encodedUrl = encodeURIComponent(url)
    const officeUrl = `https://www.pfile.com.cn/api/profile/onlinePreview?url=${encodedUrl}`
    window.open(officeUrl, '_blank')
  } else {
    Toast.fail('暂不支持该文件类型预览')
  }
}

/**
 * @description 判断传入值是否为带单位的数值，如果不是则默认添加 'px' 单位
 * @param {String|Number} value 输入值，可以是字符串或数字
 * @param {String} defaultUnit 默认单位
 * @returns {String} 返回带单位的数值
 */
export function addUnit(value, defaultUnit = 'px') {
  if (isString(value) || isNumber(value)) {
    const str = String(value).trim()
    // 常见单位正则（px、rem、em、vw、vh、% 等）
    const unitReg = /^-?\d*\.?\d+(px|rem|em|vw|vh|vmin|vmax|%)$/i

    return unitReg.test(str) ? str : `${str}${defaultUnit}`
  } else {
    throw new Error('Invalid input type. Expected a number or string.')
  }
}

/**
 * @description 获取年份范围（当前年份的前后几年）
 * @param {Number} range
 * @returns
 */
export function getYearRange(range = 5) {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let i = currentYear - range; i <= currentYear + range; i++) {
    years.push(i)
  }
  return years
}
