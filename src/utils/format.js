import dayjs from 'dayjs'

/**
 * @description 格式化时间
 * @param date 需要格式化的时间
 * @param template
 * @returns {string}
 */
export const formatDate = (date, template = 'YYYY/MM/DD HH:mm:ss') => {
  return dayjs(date).format(template)
}

/**
 * @description 格式化金额
 * @param amount
 * @param options
 * @returns {string}
 */
export function formatMoney(amount, options = {}) {
  const {
    decimalPlaces = 2, // 保留小数位数
    currencySymbol = '', // 货币符号（如 ¥ 或 $）
    thousandSeparator = ',', // 千分位分隔符
    decimalSeparator = '.', // 小数点符号
  } = options

  if (isNaN(amount)) return '--'

  const fixedAmount = parseFloat(amount).toFixed(decimalPlaces)

  const [integerPart, decimalPart] = fixedAmount.split('.')

  const formattedInt = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator)

  return `${currencySymbol}${formattedInt}${decimalPlaces ? decimalSeparator + decimalPart : ''}`
}
